{"查看": "View", "上传": "Upload", "上传文件": "Uploaded file", "文件列表": "File list", "上传文件大小不能超过": "Uploaded file can not exceed", "同意协议": "Agreement", "开户结果": "Account opening result", "前往选购产品": "Go to buy products", "申请已提交成功，3个工作日内审核结果将会以邮件方式通知，您也可以在开户页面查询开户结果。": "The application has been submitted successfully. The result will be notified by email within 3 working days. You can also check the result on the account opening page.", "失败原因": "Reason for failure", "已通过邮件方式通知": "Notified by email", "联系我们": "Contact us", "开户中, 1 开户成功, 2 开户失败，3未开户": "Account opening, 1 Account opened successfully, 2 Account opening failed, 3 No account opening.", "处理中": "Processing", "开户成功": "Account opened successfully", "开户失败": "Account opening failed", "未开户": "No account opening", "您正在申请CBiFund理财账户开通": "You are applying for CBiFund financial account opening", "规范透明": "Normative transparency", "持有公募基金牌照": "Hold a public funding license", "优质资产": "High-quality assets", "与国际知名机构合作": "Cooperation with internationally renowned institutions", "专业服务": "Professional service", "国际化专业团队": "International professional team", "董事会决议": "Board resolution", "请上传加盖企业公章的扫描件，支持pdf格式，大小不超过": "Please upload a scanned copy with the official seal of the company in pdf format, and the size should not exceed ", "下载模版": "Download template", "本人已阅读并同意签署": "I have read and agreed to sign", "基金业务开户相关协议": "Funding business account opening relevant agreement", "基金业务开户协议": "Fund Account Opening Agreement", "同意并继续": "Agree and continue", "请上传相关文件": "Please upload relevant documents", "请阅读并确认协议": "Please read and confirm the agreement", "理财产品": "Investment Products", "活期": "Money fund", "复利生息 灵活存取": "Compound interest Flexible access", "余额宝-人民币": "Yu'e Bao-RMB", "起投金额": "Minimum Investment Units", "立即购买": "Buy it now", "余额宝-美元": "Yu'e Bao-USD", "查看更多": "View for more", "固收": "Fixed income", "申赎免费 收益稳健": "Free redemption and stable income", "阳光美元固收": "Sunshine Dollar Fixed Income", "年化收益": "Annualized income", "产品期限": "Lock-Up Period", "月期": "Monthly", "股权": "Shareholding", "稀缺项目 长期配置": "Scarce items Long-term allocation", "敬请期待": "Stay tuned", "前往CBiBank APP了解": "Go to CBiBank APP to find out", "还未开通人民币账户": "Have not yet opened a RMB account", "前往开通": "Go to open", "阳光美元固收理财产品": "Sunshine dollar fixed income wealth management products", "基金编号": "Fund number", "产品风险等级：R1 - 低": "Product risk level: R1 - Low", "基金公司": "Fund company", "收益率：预期年化收益率为": "Yield: The expected annualized rate of return is", "产品实际投资期限：17个月（自理财产品募集期结束T+1日起息，按照自然日历月计算)": "The actual investment period of the product: 17 months (the interest will start on T+1 day after the end of the financing product offering period, calculated according to the natural calendar month).", "投资金额：$5,000起投，每$1,000递增，募满即止": "Investment Amount: Start from $5,000, increase every $1,000, while the fund is full.", "认购路径：仅限企业用户，通过企业网银认购": "Subscription path: Only for corporate users, subscribe through corporate online banking.", "本产品为封闭式基金，成功认购后无法撤销": "This product is a closed-end fund and cannot be revoked after successful subscription.", "收益试算：投资$100,000，计息期限，2021-07-30——2022-12-30；预计利息": "Income trial calculation: investment $100,000, interest period, 2021-07-30 - 2022-12-30; estimated interest.", "更多详情请致电官方热线": "For more details, please call the official hotline.", "温馨提示：以上资料不构成任何投资建议，请您根据个人风险偏好选择": "Reminder: The above information does not constitute any investment advice, please choose according to your personal risk preference.", "活动奖励": "Event Reward", "企业单笔认购满10万美元，给予150美元收益奖励金；企业单笔认购满5万美元，给予50美元收益奖励金": "Companies with a single subscription of US$100,000 will be given a US$150 income bonus; companies with a single subscription of US$50,000 will be given a US$50 income bonus.", "奖励金将于产品到期后一次性发放至您的认购账户中": "Rewards will be issued to your subscription account at one time after the product expires.", "常见问题": "Common questions", "募集期结束后T+1日起息": "Interest will start on T+1 day after the end of the offering period", "本产品为封闭式基金，成功认购后不支持取消或中途转让": "This product is a closed-end fund and does not support cancellation or mid-way transfer after successful subscription", "理财产品到期如何回款": "How to get money back when wealth management products expire", "到期日后1个工作日内，一次性返还本金及预期收益，如到期日为节假日，则顺延至节假日后": "Within 1 working day after the expiration date, the principal and expected income will be returned together at once. If the expiration date is a holiday, it will be postponed to after the holiday.", "产品文件": "Product documentation", "产品协议": "Product agreement", "期限": "Lock-Up Period", "美元": "US Dollar", "产品特点": "Features", "今日": "Today", "申购": "Subscription", "开始计息": "Start interest", "到期": "Maturity", "账户": "Account", "可用余额": "Available amount", "申购金额": "Subscription amount", "起投": "Start from", "累计扣除费用为": "The accumulated deduction is", "我有推荐人": "I have a recommender", "推荐码": "Referral code", "请输入推荐码（支持邀请码粘贴)": "Please enter the referral code (support invitation code paste)", "确认申购": "Confirm subscription", "我已阅读并同意本产品的相关协议": "I have read and agreed to the relevant protocol of this product", "产品状态": "Product status", "查看其他项目": "View other projects", "理财非存款，产品有风险，投资需谨慎": "Please be careful and cautious for each investment, any type of fund or investment has its own risks, and investment is not deposit.", "产品介绍": "Product description", "相关协议": "Related agreements", "手续费": "Handling fee", "切换": "Switch", "请先选择账户": "Please select an account first", "起投最小金额": "Minimum Investment Units", "起投最大金额": "Maximum Investment Units", "账户余额": "Account Amount", "请输入认购金额": "Please enter the subscription amount", "不足起投金额，请重新填写": "Insufficient investment amount, please re-fill.", "超过认购上限，请重新填写": "The subscription limit is exceeded, please re-fill.", "很遗憾，您的账户余额不足，请先充值": "Unfortunately, your account balance is insufficient, please refill first.", "请输入推荐码": "Please enter the referral code", "认购金额": "Subscription amount", "确认支付": "Confirm payment", "产品名称": "Product name", "币种": "<PERSON><PERSON><PERSON><PERSON>", "产品": "Product", "交易代码": "Trasaction code", "产品认购": "Product subscription", "查看持仓": "View holding position", "继续购买": "Continue to buy", "提交时间": "Submission Time", "支付失败": "Payment failed", "支付成功": "payment succeeded", "固收理财列表": "Fixed income wealth management list", "已售罄": "Sold out", "去开户": "Go to open an account", "交易时间": "Transaction hour", "暂无收益明细": "No income details yet", "累计收益": "Cumulative income", "起息日": "Start calculating interest date", "到期日": "Expiration date", "是否续期": "Whether to renew", "renewalTipTime": {"tip": "Please complete the selection before {time}"}, "是": "Yes", "否": "No", "持仓详情": "Holding position details", "请在2022年01月15日前完成选择": "Please complete the selection before January 15, 2022", "确认修改": "Confirm the changes", "续期起息日": "Renewal interest date start date", "续期到期日": "Renewal expiration date", "您已选择产品到期后进入下一续期周期": "You have chosen to enter the next renewal cycle after the product expires", "您已选择产品到期后不续期": "You have chosen not to renew the product after it expires", "交易单号": "Transaction number", "申请认购金额": "Application subscription amount", "认购成功金额": "Amount of successful subscription", "交易手续费": "Transaction fees", "订单编号": "Order number", "订单状态": "Order status", "认购成功": "Subscription succeeded", "认购中": "Subscription", "认购失败": "Subscription failed", "赎回详情": "Redemption details", "赎回金额": "Redemption amount", "赎回本金": "Redemption amount", "赎回收益": "Redemption earnings", "赎回手续费": "Redemption processing fees", "赎回总金额": "Redemption total amount", "净利润": "Net profit", "净收益": "Net profit", "赎回失败": "Redemption failed", "赎回确认待入账": "Redemption notice period", "待赎回": "Pending redemption", "赎回中": "Redeeming", "赎回成功": "Redemption succeeded", "赎回": "Redeem", "认购": "Subscribe", "认购订单": "subscription order", "暂无交易记录": "No current transaction record", "交易记录": "Transaction record", "活期记录": "Flexible term record", "理财持仓": "Investment Portfolio", "持有金额": "Holding amount", "总资产为外币资产折算成美元进行的资产统计，因汇率实时变动，总资产金额会有变化，仅供参考。": "Total assets are the assets statistics converted from foreign currency assets into US dollars. Due to real-time changes in exchange rates, the amount of total assets will change, which is for reference only.", "收益明细": "Earnings details", "已得收益": "Earnings", "到期后资金将自动买入下一期，如果赎回，请于前修改": "After the expiration, the funds will be automatically used for purchase for the next period. <br />If you redeem it, please modify it before {time}.", "更改": "Modify", "暂无持仓": "No current holding position", "前往选购商品": "Go to shop for goods", "前往开户": "Go to open account", "查看详情": "Details", "持有资产": "Holding assets", "昨日收益": "Yesterday earnings", "持有中": "Holding", "已结束": "Already finished", "偏移量": "Offset amount", "宽度": "<PERSON><PERSON><PERSON>", "高度": "Height", "产品详情": "Product details", "七日年化": "7-Day Annual Yield", "产品行情": "Product Quotes", "万份收益": "Earnings per 10,000", "过往业绩不代表未来表现，市场有风险，投资需谨慎": "Past performance does not represent future performance, there are risks in the market, investment should be cautious.", "资金明细": "Funding details", "总金额": "Total amount", "可用金额": "Available amount", "在途金额": "On-the-go amount", "转出": "Transfer out", "转入": "Transfer in", "收益入账": "Earnings credited into account", "成功": "succeed", "交易规则": "Transaction rules", "账号": "Account", "转入金额": "Transfer-in amount", "预计11月26日产生收益，11月27日收": "Expected to generate earnings on November 26 and close on November 27", "请输入推荐码(支持邀请码粘贴)": "Please enter the recommendation code (support invitation code paste)", "确认转入": "Confirm transfer-in", "转出金额": "Transfer-out amount", "可转出": "Can be transfer out", "确认转出": "Confirm transfer-out", "申购手续费": "Subscription fee", "确认金额": "Confirm the amount", "理财开户": "Open Investment Account", "转入规则": "Transfer in rules", "转出规则": "Transfer out rules", "T（T为交易日）日15点前转入，T+3日开始计息，计息后下一个自然日可以查看收益。": "Transfer in before 15:00 on T (T is the trading day), the interest will start calculating on T+3 day, and the earnings can be viewed on the next calendar day after the interest is calculated.", "T（T为交易日）日15点前转入，T+1日开始计息，计息后下一个自然日可以查看收益。": "Transfer in before 15:00 on T (T is the trading day), the interest will start calculating on T+1 day, and the earnings can be viewed on the next calendar day after the interest is calculated.", "T（T为交易日）日15点前转出，T+3日24:00前到账，转出当日仍有收益。": "Transfer out before 15:00 on T (T is the trading day) day, credit into account before 24:00 on T+3 day, and there is still earnings on the day of transfer.", "T（T为交易日）日15点前转出，T+1日24:00前到账，转出当日仍有收益。": "Transfer out before 15:00 on T (T is the trading day) day, credit into account before 24:00 on T+1 day, and there is still earnings on the day of transfer.", "买入时间": "Buy-in time", "开始计算收益": "Start calculating earnings", "收益到账": "Proceeds'll be accounted", "申购费率": "Subscription rate", "买入金额": "Buy-in amount", "任意金额": "Random amount", "费率": "Rate", "交易限制": "Trading restrictions", "持仓上限": "Holding position upper limit", "100元及其整数倍": "CNY100 and its integer multiples", "1000USD及其整数倍": "USD1000 and its integer multiples", "30万元": "CNY300,000", "取出时间": "Take-out time", "资金到账时间": "Fund arrival time", "收益截止时间": "Earnings deadline", "24点前": "Before 24:00", "今日 15:00前": "Before 15:00 today", "周一15:00至周二15:00前": "Monday 15:00 to Tuesday 15:00", "周二15:00至周三15:00前": "Tuesday 15:00 to Wednesday 15:00", "周三15:00至周四15:00前": "Wednesday 15:00 to Thursday before 15:00", "周四15:00至周五15:00前": "Thursday 15:00 to Friday before 15:00", "周五15:00至下周一15:00前": "Friday 15:00 to next Monday 15:00", "周三24:00前": "Before Wednesday 24:00", "周四24:00前": "Before Thursday 24:00", "周五24:00前": "Before Friday 24:00", "下周一24:00前": "Before next Monday 24:00", "下周二24:00前": "Before next Tuesday 24:00", "下周三24:00前": "Before next Wednesday 24:00", "下周四24:00前": "Before next Thursday 24:00", "下周一": "Next Monday", "预计 2022-02-21 产生收益，2022-02-20 收益到账": "It is expected to generate earnings on 2022-02-21, and the earnings has been credited into the account on 2022-02-20.", "预计12月8日24:00前到账，12月7日仍有收益": "It is expected to credit into account before 24:00 on December 8th, and there is still earnings on December 7th.", "请输入转入金额": "Please enter transfer-in amount", "可转出金额不足": "Insufficient amount for transfer-out", "确认认购": "Confirm Subscription", "支付流程": "Payment Process", "支付结果": "Payment Result", "预计": "Expected", "产生收益": "Generated Revenue", "下周二": "Next Tuesday", "下周三": "Next Wednesday", "下周四": "Next Thursday", "下周五": "Next Friday", "相关交易": "Related Transactions", "追加金额": "Additional Amount", "申请受理成功": "Application Accepted Success", "申请受理失败": "Application Accepted Failed", "确认成功": "Application Confirmed Successfully", "产品编号": "Product Number", "交易详情": "Transaction Details", "提交董事会决议": "Submission of board resolutions", "最新公告": "Notice", "恭喜您获得公司理财模块体验资格 足不出门体验全球理财服务": "Congratulations on your qualification to experience the corporate finance module; Experience global financial services at home", "年化收益率": "Annual yield", "预期年化收益": "Expected Return Rates", "网银产品": "Product", "亮点": "Advantage", "理财列表": "Financial List", "认购证明": "Subscription certificate", "下载证明": "Download certificate", "请填写邮箱账号进行申请": "Please fill in the email account to apply", "此证明将发送至您指定的邮箱中，请注意查收。": "This certificate will be sent to the mailbox you specified. Please check it.", "认购证明已发送邮箱，请注意查收": "The subscription certificate has been sent to the email, please check it", "补充资料": "Supplementary information", "支持JPG、PNG、ZIP、PDF格式文件，大小不超过": "It supports JPG, PNG, zip and PDF files with a size of no more than ", "APP在线开户": "App online account opening", "下载APP 在线签署": "Download app and sign Online", "网银文件开户": "Online banking document account opening", "下载文件 签字扫描并上传": "Download files, sign, scan and upload", "扫描二维码，下载CBiBank APP": "Scan QR code and download CBibank app", "进入APP首页点击“企业理财开户”": "Enter the home page of the APP and click \"enterprise financial management account opening\"", "纸质文件签字太麻烦？": "Is it too troublesome to sign paper documents?", "下载模板": "Download template", "试试APP在线开户方式，无纸化更方便": "Try the APP online account opening method, paperless and more convenient", "基金账户开户协议": "Fund Account Opening Agreement", "市场节日": "Market Holidays", "年市场节日": " Market Holidays", "节日": "Holiday", "日期": "Date", "马丁·路德·金纪念日": "Birthday of <PERSON>, Jr", "华盛顿诞辰日": "Washington's Birthday", "耶稣受难日": "Good Friday", "美国阵亡将士纪念日": "Memorial Day", "六月节": "Juneteenth", "美国独立纪念日": "Independence Day", "劳工节": "Labor Day", "感恩节": "Thanksgiving Day", "圣诞节": "Christmas", "元旦": "New Year's Day", "agreeExcellence": "To agree with {agreementFile} and to authorize CBiFund opening the service which the balance can transfer into Premier automatically.", "抱歉，您暂时无法购买理财产品，现有持仓不受影响": "抱歉，您暂时无法购买理财产品，现有持仓不受影响", "生息": "Yield <PERSON>", "银行账户": "Bank Account", "理财账户": "Investment Account", "卓越理财(D+0)": "Premier(D+0)", "美元余额宝": "Money Fund", "固收产品": "Fixed Income", "更多设置": "More Settings", "产品持仓": "Product Position", "随存随取": "Random Access", "灵活复利": "Flexible Compound Interest", "收益稳健": "Healthy Returns", "卓越理财": "Premier Investment", "卓越理财公告": "Premier Announcement", "因系统升级维护，卓越理财赎回暂停，\n预计恢复时间2025-07-01 4:00(UTC-4)": "Due to system upgrade and maintenance, Premier redemptions are temporarily suspended. The service is expected to resume on \nJuly 1, 2025, at 4:00 AM (UTC-4)", "卓越理财计划管理": "Premier Plan Management", "普通活期账户": "Regular Current Account", "无利息，无收益": "No interest,No Income", "加入卓越理财计划": "Join Premier <PERSON>", "活期理财": "Current Account Management", "自动转换，无需操作": "Automatic Conversion,No Operation Required", "灵活支取": "Flexible Withdrawal", "支持随时转出、兑换": "Support Transfer Out and Exchange at any Time", "修改预留金额": "Modify the Reserved Amount", "开启余额生息": "Open Balance Interest", "您已开通卓越计划，余额自动转入生息，方便快捷": "You have opened Premier Plan.The balance will automatally transfer to interest.", "关闭服务": "Service Crond Stop", "请先勾选并阅读协议": "Please check and read the agreement first.", "完成开户后可查看产品详情": "You can view the product details after openning the account.", "卓越理财计划已开启": "Premier Plan has been started.", "关闭卓越理财计划将无法享受自动生息服务": "You won't enjoy the service which can generated interest automatally if shuttind down Premier Plan.", "继续关闭": "Continue to Shut Down", "卓越理财计划已关闭": "Premier Plan has shutted down.", "设置预留金额": "Set the Reserved Amount", "请输入预留金额，预留金额需≥": "Please enter the reserved amount,which should ≥ ", "恢复默认预留金额": "<PERSON><PERSON>", "超过预留金额的部分将会在每天18:00后自动转入卓越计划账户": "The amount in excess of the reserved part will transfer into your Premier Plan amount automatically after 18 o'clock every day.", "预留金额需≥": "The reserved amount should ≥ ", "请输入预留金额": "Please enter the default reseved amount.", "配置成功": "Configure successfully.", "只能设置整数": "You can only set integers.", "申赎方式：无节假日限制，随存随取": "Thou Shalt Redeem Method: No holiday restricions, and saving as you go.", "收益情况：收益稳健，无节假日限制": "Income Situation: Healthy returns and no holiday restrictions.", "investmentThreshold": "Investment Threshold:Starting at {minAmount} {currency}，and increasing every {incrAmount} {currency}", "相关费用：0管理费，0认购费": "Correlative Charges: No administrative fee and subscription fee.", "卓越理财计划余额": "The Balance of Premier Plan", "卓越理财余额": "The Balance of Premier Plan", "卓越计划余额": "The Balance of Premier Plan", "资金归集": "Fund Concentration", "资金将自动从卓越理财转至标准美元账户，并通过标准美元账户进行汇出": "The cash will transfer from Premier into your standard US dollar account, and be remitted through the standard US dollar account.", "转入方式": "Transfer Mode", "划转金额": "Transfer Amount", "确定划转": "Transfer Determination", "确定转出": "Confirm Transfer", "全部转出": "Transfer all out", "产品公告": "Product Announcement", "实时互转": "Real-time conversion", "再想想": "Think again", "卓越理财产品服务协议": "Premier Scheme Service Agreement", "转入时间": "Transfer in time", "转出时间": "Transfer out time", "转入成功": "Transferred in", "转入失败": "Transfer Failure", "转出成功": "Transferred out", "转出失败": "Roll-out Failure", "操作失败，请联系客服": "Operation failure, please contact customer service", "未开通卓越理财，请开通卓越理财后再试": "You have not subscribed Premier", "产品暂时无法转出，请重试或联系客服": "Premier cannot be redeemed temporarily, please try again or contact customer service", "系统异常，请稍后再试": "System error, please try again later", "请先完成开通理财账户": "Please Open A Fund Account First", "前去开户": "Go", "还未开通理财账户": "You have not Open A Fund Account", "温馨提示：我行在回复询证函时，银行存款事项仅回复银行账户存款余额部分。": "Tips: When replying to the confirmation letter, our bank only replies to the deposit balance of the bank account.", "您申购的理财资金，将自动从银行账户划转至您的理财账户。": "The financial funds you subscribed for will be automatically transferred from your bank account to your financial account.", "我已知晓并认可该方案": "I know and accept", "募集开始": "Start Raising Money", "募集结束": "Finish Raising Money", "产品代码": "Product Code", "支持续期选择": "Support for Renewal Options", "到期还本付息": "Repay Capital with Interest at Maturity", "最近一个到期日": "The Lastest Due Date", "第一期到期": "The First Due Date", "您当前暂时无法申请开通理财账户，感谢您的配合": "You are currently unable to apply for a fund account, thank you for your cooperation.", "我已知晓": "OK", "拒绝理由": "Reasons for rejection", "CRS信息声明": "CRS Information Statement", "信息声明": "Information Statement", "我知悉，我所提供的信息是按照CBi Sunshine Fund PCC Limited公司投资账户的使用条款的全部规定而提供的，并且也清楚该公司如何使用和分享我提供的信息。": "I understand that the information supplied by me is covered by the full provisions of the terms and conditions governing the Account Holder’s relationship with CBI Sunshine Fund PCC Limited setting out how it may use and share the information supplied by me.", "本人知晓，本表所含信息以及有关账户持有人和任何应报告的账户的信息可能会被报告给该帐户所在国家/司法管辖区的税务机关，并将根据国家间金融账户信息交换协议的有关规定，与其他国家/司法管辖区的税务机关交换。": "I acknowledge that the information contained in this form and information regarding the Account Holder and any Reportable Account(s) may be reported to the tax authorities of the country/jurisdiction in which this account(s) is/are maintained and exchanged with tax authorities of another country/jurisdiction or countries/jurisdictions in which the Account Holder may be tax resident pursuant to intergovernmental agreements to exchange financial account information.", "我证明，我已被授权代表账户持有人签署本表所涉及的所有账户。": "I certify that I am authorised to sign for the Account Holder in respect of all the account(s) to which this form relates.", "我声明，就我所知和所信，本声明中的所有陈述都是正确和完整的。": "I declare that all statements made in this declaration are, to the best of my knowledge and belief, correct and complete.", "我承诺，若发生任何影响到该表格第一部分中所列账户持有人的税务居民身份的变更事项，或导致本表格所包含的相关信息不正确或不完整，我将在上述变更发生后的30天内向CBI Sunshine Fund PCC Limited公司提供经适当更新的的自我证明和声明。": "I undertake to advise CBI Sunshine Fund PCC Limited within 30 days of any change in circumstances which affects the tax residency status of the Account Holder identified in Part 1 of this form or causes the information contained herein to become incorrect or incomplete, and to provide CBI Sunshine Fund PCC Limited with a suitably updated self-certification and Declaration within 30 days of such change in circumstances.", "请确认": "Confirmed", "确定提交": "Submit", "恭喜您提交成功": "Congratulations on your successful submission", "有": "Yes", "无": "No", "非金融机构": "Active NFE", "金融机构": "Financial Institution", "存款机构，托管机构": "Custodial Institution, Depository Institution", "投资实体": "Investment Entity", "其他投资实体": "Other Investment Entity", "基于收入和资产状况被认定为非金融机构": "active NFEs by reason of income and assets", "上市公司或者上市公司的关联公司": "publicly traded NFEs", "政府机构，国际组织，中央银行，以及其完全拥有的其他机构": "Governmental Entities, International Organisations, Central Banks, or their wholly owned Entities", "非金融集团中的控股企业": "holding NFEs that are members of a nonfinancial group", "创业企业": "start-up NFE", "非金融集团中的财务中心": "treasury centres that are members of a nonfinancial group", "非营利性非金融机构": "non-profit NFEs", "其他机构": "Passive NFE", "账户持有人居住的国家/司法管辖区不向其居民发放": "The country/jurisdiction where the Account Holder is resident does not issue TINs to its residents", "账户持有人因其他原因无法获得纳税人识别号或同等号码": "The Account Holder is otherwise unable to obtain a TIN or equivalent number", "不需要纳税人识别号": "No TIN is required", "请输入GIIN": "Please enter GIIN", "FUND_CRS": {"企业名称": "Legal Name of Entity/Branch", "注册国家/地区": "Country of incorporation or organisation", "公司注册地址": "Current Residence Address", "实体类型": "Entity Type", "全球中介机构识别号": "GIIN", "税务居住地国家/管辖区": "Country/Jurisdiction of tax residence", "拥有多个税务居住地国家/管辖区，请点击添加": "If your company has more than one tax residency countries/jurisdictions, please click on \"Add\"", "拥有更多最终受益所有人，请点击添加": "If you have more Ultimate Beneficial Owners, please click on \"Add\".", "最终受益所有人(UBO)身份信息": "Ultimate Beneficial Owner (UBO) identity information.", "姓氏": "Family Name or Surname", "名字": "First or Given Name", "中间名": "Middle Name", "出生日期": "Date of Birth (dd/mm/yyyy)", "出生国家": "Country of Birth", "出生城市": "Town or City of Birth", "纳税人识别号码": "TIN", "当前居住地": "Current Residence Address", "国家": "Country", "地址": "e.g. AddressFree", "邮寄地址": "Mailing Address", "依照监管要求，为更好的为您提供财富管理服务，CBiFund将会收集您的CRS信息。如果以下选项无误，您可以直接点击【确认提交】按钮；如与公司实际情况不符合，可以在页面中修改。": "In accordance with regulatory requirements, CBiFund will collect your CRS information in order to better provide you with wealth management services. If the following options are correct, you can directly click the \"Confirm submission\" button; If it does not conform to the actual situation of the company, you can modify it on the page."}, "支持输入字母 数字 - , . / ": "Support input :alphabet number  - , . / ", "税务信息更新": "Tax Information Update", "卓越理财为D+0到账产品，到账时间受银行处理速度影响，已为您加速转出，预计2小时内到账": "Premier is a wealth management product with D+0 account crediting, with the arrival time subject to bank processing speed. We have expedited your transfer, and expect it to be credited within 2 hours.", "银行确认": "Bank confirmation", "资金到账": "Funds received", "预计2小时内到账": "Expected to be credited within 2 hours.", "查看交易明细": "Transaction Detail", "尊敬的客户，您开通的卓越理财迎来重大升级，将逐步支持您在CBiBank的每个币种账户购买卓越理财，无需换汇，转入即可生息，赎回最快实时到账，无节假日限制。": "Dear customer, the Premier service you have activated is undergoing a major upgrade. It will gradually support purchasing Premier in each currency account at CBiBank, with no need for currency exchange. Funds will start accruing interest upon transfer, and redemptions can be processed with instant real-time settlement, with no holiday restrictions.", "卓越理财2.0全新升级：新增欧元、离岸人民币及港币理财，助您把握全球市场机遇！灵活提取，分散风险，优化收益，并利用汇率对冲实现额外收益，轻松实现资产保值增值！": "Premier Scheme 2.0 – Brand New Upgrade : Introducing new investment options in EUR, CNY, HKD to help you seize global market opportunities! Enjoy flexible withdrawals, risk diversification, optimized returns, and additional gains through currency hedging, ensuring easy asset preservation and growth!", "持有": "Holdings", "3050账户余额10,000.00 USD，等同本金转入卓越理财一年预估收益12.00 USD": "3050 Account Balance 10,000.00 USD, estimated annual return of 12.00 USD if transferred to Premier.", "美元(USD)卓越理财": "USD Premier", "当日起息，每日计息，利息每周三发放": "Interest starts from the same day, calculated daily, with interest paid every Wednesday.", "支持实时转出，无节假日限制，最快实时到账": "Supports real-time withdrawals with no holiday restrictions, and the fastest real-time settlement.", "您已开通": "You have activated the ", "每日自动划转时间": "Daily Automatic Transfer Time", "美国时间18：00": "18:00 U.S. Time", "卓越理财产品起投金额": "Minimum Investment Amount for Premier Products", "每周利息发放时间": "Weekly Interest Payment Time", "美国时间周三": "Wednesday, U.S. Time", "预留金额": "Reserved Amount", "预留金额：每日卓越理财自动划转时，默认将超过预留金额的部分转入卓越理财生息。": "Reserved Amount: During the daily automatic transfer to Premier, the default setting is to transfer any amount exceeding the reserved amount for interest accrual.", "立即转入": "Transfer Now", "每日定时自动资金划转": "Daily Scheduled Automatic Fund Transfer", "每日生息，每周派息": "Daily Interest Accrual, Weekly Payout", "随时支取最快实时到账": "Withdraw Anytime with Instant Real-Time Processing", "汇款可用卓越账户支付": "Remittances Payable via Premier Account", "确认修改为": "Confirm modification to ", "卓越理财支持实时转出，无节假日限制，最快实时到账": "Premier supports real-time withdrawals with no holiday restrictions, ensuring the fastest possible real-time settlement.", "您还未开通对应币种账户，请先去开通": "You haven't activated the corresponding currency account. Please activate it first.", "每日自动划转": "Daily automatic transfer", "天天享受利息": "Enjoy interest every day", "每周派息到账": "Weekly interest distribution", "随时灵活支取": "Flexible withdrawal at any time", "一键快捷汇款": "One-click quick transfer", "遇到一点问题，请稍后重试或联系*******************": "An issue occurred. Please try again later <NAME_EMAIL>", "投资理财": "Investment Management", "理财对账单": "Financial Statement", "查询时间": "Query Time", "申请对账单": "Request Statement", "近3个月": "Last 3 Months", "近6个月": "Last 6 Months", "近12个月": "Last 12 Months", "申请时间": "Request Time", "时间周期": "Time Period", "申请邮箱": "Request Email", "您的对账单申请提交已成功": "Your statement request has been successfully submitted", "预计10分钟内发送至指定邮箱，请耐心等待，谢谢。": "Expected to be sent to the specified email within 10 minutes. Please wait patiently. Thank you.", "查看申请记录": "View Request History", "加载中，请稍后": "Loading, please wait.", "最长可查询一年交易记录": "You can view transaction history for up to one year", "自定义时间": "Custom Time Range", "对账单支持每次导出不超过一年的数据": "Each statement export supports data for up to one year only."}