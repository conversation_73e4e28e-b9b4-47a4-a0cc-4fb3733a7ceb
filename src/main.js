import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import axios from '@/utils/http/axios'
// 样式
import '@/assets/styles/global.css'
import '@/assets/styles/reset.css'
import '@/assets/styles/control.css'
// 环境变量
import '@/config'
// 全局变量及方法
import '@/utils/global/extend'
import core from '@/utils/global/core'
import format from '@/utils/global/format'
import params from '@/utils/global/params'
import media from '@/utils/media'
// 新手引导下掉
// import VueIntro from 'vue-introjs'
// 剪切板
import VueClipboard from 'vue-clipboard2'
// 按需引入 element-ui
import { Button, Loading, Message, Table, TableColumn, Pagination, Select, Option, Input, Dialog, DatePicker, Form, FormItem, Row, Col, Checkbox, CheckboxGroup, Radio, Tooltip, Link, Progress, Carousel, CarouselItem, RadioGroup, Tabs, TabPane, Steps, Step, Switch, Empty, InputNumber, Autocomplete, Calendar, Image, Dropdown, DropdownMenu, DropdownItem, Cascader, Upload } from 'element-ui'
import ElementLocale from 'element-ui/lib/locale'
// 自定义element组件样式
import '@/assets/styles/element/index.js'
// 国际化
import i18n from '@/lang'
// 全局组件
import '@/components'
// 全局指令
import '@/utils/directives'
// 全局过滤器
import '@/utils/filters'
// 全局svg
import '@/assets/icons'
// 自定义组件
import TMessageBox from '@/components/tMessageBox'
import TLoading from '@/components/tLoading'

import GeeTest from '@cbibank/geetest'
import geeTestApi from '@/api/geeTest'

// 引入组件
import { Survey } from '@cbibank/survey'
// 引入样式
import '@cbibank/survey/styles/index.css'
import '@cbibank/survey/theme/v1/index.css'
// import '@cbibank/survey/addtion.js' // 由于该项目已经引入了element相关组件 所以忽略该项

// 注册组件
Vue.use(Survey)

Vue.use(GeeTest, {
  appCode: 'cbibank-web-corporbank',
  authApi: {
    getAccess: geeTestApi.registerGeeTest
  }
})

DatePicker.props.clearable = false
Vue.use(core)
Vue.use(format)
Vue.use(params)
Vue.use(media)
Vue.use(VueClipboard)

Vue.use(Button)
Vue.use(Input)
Vue.use(Select)
Vue.use(Option)
Vue.use(Dialog)
Vue.use(Loading)
Vue.use(Autocomplete)
Vue.use(Table)
Vue.use(TableColumn)
Vue.use(Pagination)
Vue.use(Select)
Vue.use(Option)
Vue.use(DatePicker)
Vue.use(Checkbox)
Vue.use(CheckboxGroup)
Vue.use(Form)
Vue.use(FormItem)
Vue.use(Row)
Vue.use(Col)
Vue.use(Radio)
Vue.use(Tooltip)
Vue.use(Row)
Vue.use(Col)
Vue.use(Link)
Vue.use(Progress)
Vue.use(Carousel)
Vue.use(CarouselItem)
Vue.use(RadioGroup)
Vue.use(Tabs)
Vue.use(TabPane)
Vue.use(Steps)
Vue.use(Step)
Vue.use(Switch)
Vue.use(InputNumber)
Vue.use(Calendar)
Vue.use(Image)
Vue.use(Dropdown)
Vue.use(DropdownMenu)
Vue.use(DropdownItem)
Vue.use(Cascader)
Vue.use(Upload)

// Vue.use(VueIntro)
Vue.use(Empty)

// 由于element-ui对于Message的架构设计，并无install方法。所以这里不能调用Vue.use()。如果想用this.$message，请使用Vue.prototype.$mesage=Message来手动注册
// Vue.use(Message)

ElementLocale.i18n((key, value) => i18n.t(key, value))
Vue.use(TLoading)
const msgObj = {
  center: true,
  duration: 3000,
  showClose: false
}

Vue.prototype.$tmessage = TMessageBox
Vue.prototype.$talert = TMessageBox.alert
Vue.prototype.$tconfirm = TMessageBox.confirm

Vue.prototype.$err = function (msg, obj) {
  Message({
    ...msgObj,
    ...obj,
    message: this.$t(msg),
    type: 'error'
  })
}

Vue.prototype.$success = function (msg, obj) {
  Message({
    ...msgObj,
    ...obj,
    message: this.$t(msg),
    type: 'success'
  })
}

Vue.prototype.$warn = function (msg, obj) {
  Message({
    ...msgObj,
    ...obj,
    message: this.$t(msg),
    type: 'warning'
  })
}

Vue.prototype.$axios = axios

Vue.config.productionTip = false
new Vue({
  router,
  store,
  i18n,
  render: h => h(App)
}).$mount('#app')
