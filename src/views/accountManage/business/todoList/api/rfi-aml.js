import api, { cacheAxios } from '@/utils/http/axios'
import { CBI_LS_CORPORBANK } from '@/utils/http/base-url'

export default {
  /**
  * 查询RFI枚举
  */
  getRfiAmlTreeList (params = {}, config = {}) {
    return cacheAxios.post(`${CBI_LS_CORPORBANK}/api/v1/corpBacklog/tree`, params, config)
  },
  /**
  * 查询RFI待办详情
  * @param {string} rfiNo 号码
  */
  getRfiAmlDetail (params = {}, config = {}) {
    return api.post(`${CBI_LS_CORPORBANK}/api/v1/corpBacklog/queryDetail`, params, config)
  },
  /**
  * 创建RFI
  * @param {string} refNo 业务号
  */
  createRfiAml (params = {}, config = {}) {
    return api.post(`${CBI_LS_CORPORBANK}/api/v1/corpBacklog/create`, params, config)
  },
  /**
  * 提交RFI
  * @param {string} rfiNo 号码
  * @param {string} detailContent JSON体
  */
  submitRfiAml (params = {}, config = {}) {
    return api.post(`${CBI_LS_CORPORBANK}/api/v1/corpBacklog/submit`, params, config)
  }
}
