<template>
  <ModuleSection
    class="rfi-aml-result"
    :title="$t('待办事项')">
    <DetailList></DetailList>
    <Result
      :status="status"
      :tipMessage="status === 'FINISHED' ? $t('您的资料已成功提交') : $t('待办已关闭，无需近一步操作')">
      <div class="result-slot">
        <div class="result-message" v-if="status === 'FINISHED'">
          <p class="message">{{ $t('系统已确认您完成了资料提交，请耐心等待银行审核与后续处理。') }}</p>
          <p class="message">{{ $t('无需再次提交，如有进一步进展我们将通过网银通知您。') }}</p>
        </div>
        <div class="result-message" v-if="status === 'CLOSED'">
          <p class="message">{{ $t('该任务已关闭，可能因提交时间超出有效期或资料已成功提交。') }}</p>
          <p class="message">{{ $t('系统不再接受新的提交，无需重复操作，感谢您的配合。') }}</p>
        </div>
        <el-button type="info" @click="handleBack">{{ $t('返回') }}</el-button>
      </div>
    </Result>
  </ModuleSection>
</template>

<script>
import DetailList from './components/DetailList'
import rfiAmlApi from '../api/rfi-aml'

export default {
  components: {
    DetailList
  },
  data () {
    return {
      // FINISHED CLOSED
      status: '',
      rfiNo: '',
      redirect: '',
      rfiDetail: {}
    }
  },
  computed: {},
  watch: {},
  created () {
    const { rfiNo, redirect } = this.$route.query
    this.rfiNo = rfiNo
    this.redirect = redirect
    this.getRfiAmlDetail()
  },
  mounted () {},
  methods: {
    getRfiAmlDetail () {
      const { rfiNo } = this
      if (!rfiNo) {
        return
      }
      rfiAmlApi.getRfiAmlDetail({
        rfiNo
      }).then(res => {
        if (res) {
          const { rfiStatus } = res
          this.status = rfiStatus
        }
      }).catch(error => {
        console.error(error)
      })
    },
    handleBack () {
      const { redirect } = this
      if (redirect) {
        this.$router.push({
          name: redirect
        })
      } else {
        this.$router.push({
          name: 'PendingTodoList'
        })
      }
    }
  }
}
</script>

<style scoped lang="less">
.rfi-aml-result {
  .result-slot {
    text-align: center;
    .result-message {
      width: 500px;
      padding: 35px;
      margin: 0 auto 40px;
      text-align: center;
      border-radius: 10px;
      border: 1px dashed #D7DBEC;
      .message {
        font-size: 14px;
        color: #5A607F;
        line-height: 24px;
      }
    }
  }
}
</style>
