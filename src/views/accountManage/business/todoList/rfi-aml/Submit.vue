<template>
  <ModuleSection
    class="rfi-aml-result"
    :title="$t('待办事项')">
    <DetailList :rfiDetail="rfiDetail"></DetailList>
    <div class="survey-container">
      <PlatformPage
        ref="platformPage"
        v-model="pageData"
        :source-api="sourceApi"
        :editable="false"
        :disabled="rfiDetail.rfiStatus === 'WAITING' ? false : true"
        @submit="handleSubmit">
      </PlatformPage>
    </div>
  </ModuleSection>
</template>

<script>
import DetailList from './components/DetailList'
import rfiAmlApi from '../api/rfi-aml'
import { sourceApi } from './config/survey'

export default {
  components: {
    DetailList
  },
  data () {
    return {
      sourceApi,
      rfiDetail: {},
      pageData: {},
      rfiNo: '',
      redirect: '' // 提交结果重定向路由
    }
  },
  computed: {
  },
  watch: {},
  created () {
    const { rfiNo, redirect } = this.$route.query
    this.rfiNo = rfiNo
    this.redirect = redirect
    this.getRfiAmlDetail()
  },
  mounted () {},
  methods: {
    getRfiAmlDetail () {
      const { rfiNo } = this
      rfiAmlApi.getRfiAmlDetail({
        rfiNo
      }).then(res => {
        if (res) {
          // title titleCn rfiOverdueTime rfiStatus rfiStatusCn rfiStatusEn
          const { title, titleCn, rfiOverdueTime, rfiStatus, detailContent } = res
          this.rfiDetail = {
            title,
            titleCn,
            rfiStatus,
            rfiOverdueTime
          }
          this.pageData = JSON.parse(detailContent)
        }
      })
    },
    submitRfiAml ({ detailContent = '' }) {
      const { rfiNo, redirect } = this
      const loading = this.$loading()
      rfiAmlApi.submitRfiAml({
        rfiNo,
        detailContent
      }).then(res => {
        if (res) {
          this.$router.push({ name: 'RfiAmlResult', query: { rfiNo, redirect } })
        }
      }).catch(error => {
        console.error(error)
      }).finally(() => {
        loading.close()
      })
    },
    handleSubmit (pageData) {
      this.submitRfiAml({
        detailContent: JSON.stringify(pageData)
      })
    }
  }
}
</script>

<style scoped lang="less">
.rfi-aml-result {
  .survey-container {
    min-height: 500px;
    padding: 10px;
    background: #FFFFFF;
    box-shadow: 0px 6px 20px 0px rgba(90,96,127,0.1);
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #F6F7FC;
  }
}
</style>
