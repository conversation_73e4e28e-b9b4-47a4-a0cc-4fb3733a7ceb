<template>
  <ul class="detail-list">
    <li class="detail-item">
      <span class="label">{{ $t('事项：') }}</span>
      <span class="value">{{ lang === 'zh-CN' ? rfiDetail.titleCn : rfiDetail.title }}</span>
    </li>
    <li class="detail-item">
      <span class="label">{{ $t('失效日期：') }}</span>
      <span class="value">{{ rfiDetail.rfiOverdueTime }}</span>
    </li>
  </ul>
</template>

<script>
import { mapState } from 'vuex'

export default {
  props: {
    rfiDetail: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  data () {
    return {
    }
  },
  computed: {
    ...mapState('app', ['lang'])
  },
  watch: {},
  created () {},
  mounted () {},
  methods: {
  }
}
</script>

<style scoped lang="less">
.detail-list {
  margin-bottom: 40px;
  .detail-item {
    display: flex;
    margin-bottom: 30px;
    &:last-child {
      margin-bottom: 0;
    }
    .label {
      width: 150px;
      font-weight: 500;
      font-size: 14px;
      color: #333752;
      line-height: 16px;
    }
    .value {
      flex: 1;
      font-size: 14px;
      color: #5A607F;
      line-height: 16px;
    }
  }
}
</style>
