<template>
  <div class="submit-rfi-aml">
    <div class="description-text">
      {{ $t('若您有相关证明材料（如合同、发票等），可在此上传，有助于加快后续合规审核流程。此步骤为可选项，不影响汇款结果。请在30分钟内完成提交。若超时且仍有需要，请等待待办事项通知。') }}
      <div class="submit-btn">
        <i class="el-icon-loading" v-show="loading"></i>
        <el-link :underline="true" @click="handleCreateRfiAml">{{ $t('上传补充材料（可选）') }}</el-link>
      </div>
    </div>
  </div>
</template>

<script>
import rfiAmlApi from '../../api/rfi-aml'

export default {
  props: {
    refNo: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      loading: false
    }
  },
  computed: {},
  watch: {},
  created () {},
  mounted () {},
  methods: {
    createRfiAml () {
      const { refNo } = this
      return rfiAmlApi.createRfiAml({
        refNo
      })
    },
    handleCreateRfiAml () {
      this.loading = true
      this.createRfiAml().then(res => {
        if (res) {
          const { rfiNo } = res
          this.$router.push({
            name: 'RfiAmlSubmit',
            query: {
              rfiNo,
              redirect: 'Home'
            }
          })
        }
      }).catch(error => {
        console.error(error)
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped lang="less">
.submit-rfi-aml {
  .description-text {
    margin-bottom: 16px;
    // padding: 12px;
    border-radius: 4px;
    color: #5a607f;
    font-size: 14px;
    line-height: 1.5;
    .submit-btn {
      display: inline-flex;
      justify-content: center;
      align-items: center;
      color: @blue;
      .el-icon-loading {
        margin-right: 2px;
      }
    }
  }
}
</style>
