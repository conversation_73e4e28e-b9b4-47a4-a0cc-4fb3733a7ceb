import rfiAmlApi from '../../api/rfi-aml'
import uploadApi from '@/api/upload'

/*
此文件用来统一维护问卷API:
[参考文档](https://npm.prodcbi.com/-/web/detail/@cbibank/survey)
*/
export const sourceApi = {
  getEnums (params = {}, config = {}) {
    return rfiAmlApi.getRfiAmlTreeList(params, config)
  },
  uploadFile (params = {}, config = {}) {
    return uploadApi.uploadOssFile(params, config)
  },
  downloadFile (params = {}, config = {}) {
    return uploadApi.downloadOssFile(params, config)
  }
}
