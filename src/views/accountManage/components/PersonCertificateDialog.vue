<template>
  <el-dialog
    :title="$t('上传附件')"
    :visible="visible"
    :close-on-click-modal="false"
    :show-close="false"
    width="1060px"
    class="certificate-manage-dialog"
  >
    <el-form ref="form" :model="formData" :rules="optType === 'view' ? {} : rules" :disabled="optType === 'view'">
      <el-row type="flex" class="form-row">
        <el-col :span="12" class="form-item-col">
          <el-form-item :label="$t('人员姓名')" prop="relatedNameEn">
            <!-- 新增可以选择人员 -->
            <el-select
              v-if="optType === 'add'"
              v-model="formData.relatedNameEn"
              style="width: 100%"
            >
              <el-option v-for="(item, index) in nameList" :key="index" :label="$t(item)" :value="item" />
            </el-select>
            <!-- 修改、查看只能查看，且不可编辑 -->
            <el-input v-else v-model="formData.relatedNameEn" disabled/>
          </el-form-item>
        </el-col>
        <el-col style="margin-left: 40px;" :span="12" class="form-item-col">
          <el-form-item :label="$t('证件号码')" prop="certificateNo">
            <el-input v-model="formData.certificateNo" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row type="flex" class="form-row">
        <el-col :span="12" class="form-item-col">
          <!-- 只有新增、更新并且勾选了更换主证件才可修改 -->
          <el-form-item :label="$t('证件类型')" prop="certificateType">
            <el-select
              v-model="formData.certificateType"
              :disabled="certDisabled"
              style="width: 100%"
              @change="changeCertType"
            >
              <el-option v-for="(item, index) in certList" :key="index" :label="$t(item.showName)" :value="item.name" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col style="margin-left: 40px;" :span="12" class="form-item-col">
          <el-form-item
            :label="$t('生效日期')"
            prop="effectiveDate">
            <el-date-picker
              v-model="formData.effectiveDate"
              :placeholder="$t('请选择')"
              style="width: 100%;"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptionsStart"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row type="flex" class="form-row ">
        <el-col :span="12" class="form-item-col">
          <!-- 签发国家/地区生产有许多空数据，如果为空且更新情况下可以允许选择 -->
          <el-form-item :label="$t('签发国家/地区')" prop="signCountry">
            <el-select v-model="formData.signCountry" :disabled="certDisabled && !(optType === 'update' && !formData.signCountry)" filterable style="width: 100%">
              <el-option
                v-for="(item, index) in signCountryList"
                :key="index"
                :label="$t(item.showName)"
                :value="item.name" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col style="margin-left: 40px;" :span="12" class="form-item-col">
          <el-form-item
            :label="$t('失效日期')"
            prop="expirationDate">
            <div class="expiration-date-container">
              <el-date-picker
                v-if="!expirationDateLongChecked"
                v-model="formData.expirationDate"
                :placeholder="$t('请选择')"
                style="width: 100%;"
                :disabled="expirationDateLongChecked"
                value-format="yyyy-MM-dd"
                :picker-options="pickerOptionsEnd"
              />
              <el-date-picker
                v-else
                value=""
                style="width: 100%;"
                :disabled="expirationDateLongChecked"
                value-format="yyyy-MM-dd"/>
              <div class="check-container">
                <el-checkbox
                  v-model="expirationDateLongChecked"
                  @change="handleChangeBox"
                >
                  {{ $t('长期有效') }}
                </el-checkbox>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 主证件进来一定是主证件，写死即可 -->
      <el-row type="flex" class="form-row">
        <el-col :span="12" class="form-item-col">
          <el-form-item :label="$t('是否主证件')">
            <el-input :value="$t(personCertType === 'main' ? '是' : '否')" disabled />
          </el-form-item>
        </el-col>
        <el-col style="margin-left: 40px;" :span="12" class="form-item-col" />
      </el-row>
      <!-- 特殊情况，更换主证件上传 只有主证件、修改或者查看才展示 -->
      <el-row v-if="personCertType === 'main' && optType !== 'add'" style="margin-bottom: 30px;">
        <el-col :span="12" class="form-item-col">
          <div>
            <el-checkbox v-model="formData.changeMainDoc" :disabled="optType === 'view'">
              {{ $t('特殊情况，更换主证件上传') }}
            </el-checkbox>
          </div>
          <p class="upload-tips">
            <span>*</span>
            {{ $t('特殊情况仅限于国籍变更及不可抗力因素导致的主证件不能更换的情况；主证件是指在银行留存的主要证件，只能为护照和身份证！') }}
          </p>
        </el-col>
      </el-row>
    </el-form>
    <!-- 中国居民身份证 -->
    <div v-if="formData.certificateType === 'ID'" class="id-card-list">
      <div class="id-card-item">
        <p>{{ $t('正面') }}</p>
        <UploadFile
          :file-list="cardFrontList"
          :maxFiles="1"
          :disabled="optType === 'view'"
          accept="image/png,image/jpeg,image/jpg,application/pdf"
          uploadType="customer"
        />
      </div>
      <div class="id-card-item">
        <p>{{ $t('反面') }}</p>
        <UploadFile
          :file-list="cardBackList"
          :maxFiles="1"
          :disabled="optType === 'view'"
          accept="image/png,image/jpeg,image/jpg,application/pdf"
          uploadType="customer"
        />
      </div>
    </div>
    <!-- 证件上传 -->
    <UploadFile
      v-else
      :file-list="fileList"
      :maxFiles="1"
      :disabled="optType === 'view'"
      accept="image/png,image/jpeg,image/jpg,application/pdf"
      uploadType="customer"
    />
    <!-- 审核状态、原因 -->
    <div v-if="optType === 'view'" class="audit-result-info"  :class="{'audit-pass': detailInfo.checkStatus === 'AUDIT_SUCCESS', 'audit-fail': detailInfo.checkStatus === 'AUDIT_REJECT'}">
      <div class="audit-info-item">
        <label>{{ $t('审核状态') }}</label>
        <p>{{ $t(detailInfo.checkStatusDesc) }}</p>
      </div>
      <!-- 审核拒绝展示拒绝原因 -->
      <div v-if="detailInfo.checkStatus === 'AUDIT_REJECT'" class="audit-info-item">
        <label>{{ $t('拒绝原因') }}</label>
        <p>{{ detailInfo.checkRemark }}</p>
      </div>
    </div>
    <p class="upload-tips">
      <span>*</span>
      {{ $t('注：证件附件仅支持jpg、jpeg、png、pdf四种格式，单个文件不超过10MB。') }}
    </p>
    <!-- 更新、新增相关操作 -->
    <span v-if="optType === 'update' || optType === 'add'" slot="footer" class="dialog-footer">
      <el-link type="primary" @click="$emit('close')">
        {{ $t('返回') }}
      </el-link>
      <el-button
        :loading="loading"
        class="confirm-btn"
        type="primary"
        style="margin-left: 30px;"
        @click="confirmUpload"
      >
        {{ $t('确认上传') }}
      </el-button>
    </span>
    <!-- 查看操作 -->
    <span v-else slot="footer" class="dialog-footer">
      <el-button class="btn140" type="info" @click="$emit('close')">{{ $t('返回') }}</el-button>
      <!-- 审核拒绝展示重新上传 -->
      <el-button
        v-if="currentCertItem.checkStatus === 'AUDIT_REJECT' && optType === 'view'"
        class="confirm-btn"
        type="primary"
        style="margin-left: 30px;"
        @click="reloadUpdate"
      >
        {{ $t('重新上传') }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import dayjs from 'dayjs'
import { mapState } from 'vuex'
import api from '../info/api/certManage'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 当前查看、更新的证件信息
    currentCertItem: {
      type: Object,
      default: function () {
        return {}
      }
    },
    // 区分查看、更新、新增
    optType: {
      type: String,
      default: 'view' // view、update、add
    },
    // 区分主证件还是辅助证件: main、other
    personCertType: {
      type: String,
      default: 'other'
    }
  },
  data () {
    return {
      expirationDateLongChecked: false, // 长期有效
      nameList: [], // 人员姓名
      certType: [], // 证件类型
      fileList: [], // 文件列表
      cardFrontList: [], // 身份证正面
      cardBackList: [], // 身份证反面
      // 表单
      loading: false,
      formData: {
        relatedNameEn: '',
        certificateNo: '',
        certificateType: '',
        effectiveDate: '',
        expirationDate: '',
        signCountry: '',
        changeMainDoc: ''
      },
      detailInfo: {} // 用来存储请求详情数据
    }
  },
  computed: {
    ...mapState('base', ['selectMap']),
    pickerOptionsStart () {
      return {
        disabledDate: (time) => {
          return dayjs(time).isAfter(dayjs())
        }
      }
    },
    pickerOptionsEnd () {
      return {
        disabledDate: (time) => {
          // 今天之前，最多30年，大于生效日期
          return dayjs(time).isBefore(dayjs().subtract(1, 'day')) ||
            dayjs(time).isBefore(dayjs(this.formData.effectiveDate)) ||
            dayjs(time).isAfter(dayjs(this.formData.effectiveDate).add(30, 'year'))
        }
      }
    },
    rules () {
      return {
        relatedNameEn: [
          { required: true, message: this.$t('请选择'), trigger: 'change' }
        ],
        certificateNo: [
          { required: true, message: this.$t('请输入'), trigger: 'blur' }
        ],
        certificateType: [
          { required: true, message: this.$t('请选择'), trigger: 'change' }
        ],
        effectiveDate: [
          { required: true, message: this.$t('请选择'), trigger: 'change' }
        ],
        signCountry: [
          { required: true, message: this.$t('请选择'), trigger: 'change' }
        ],
        expirationDate: [
          { required: true, message: this.$t('请选择'), trigger: 'change' }
        ]
      }
    },
    // 证件类型、签发机构是否可以编辑
    certDisabled () {
      const { optType, formData } = this
      const { changeMainDoc } = formData

      if (optType === 'add') return false
      else if (optType === 'update' && changeMainDoc) return false

      return true
    },
    // 证件类型列表
    certList () {
      // 证件类型列表不再区分主证件、辅助证件
      const { certExpireRelatedMainCertType, certExpireRelatedAssistCertType } = this.selectMap

      return [...(certExpireRelatedMainCertType || []), ...(certExpireRelatedAssistCertType || [])]
      // const { certExpireRelatedMainCertType, certExpireRelatedAssistCertType } = this.selectMap
      // const { personCertType } = this

      // if (personCertType === 'main') {
      //   // 主证件
      //   return certExpireRelatedMainCertType || []
      // } else {
      //   // 辅助证件
      //   return certExpireRelatedAssistCertType || []
      // }
    },
    // 签发机构
    signCountryList () {
      const { selectMap, formData } = this
      const { signCountryType: countryList } = selectMap
      const { certificateType } = formData
      let findSignCountry = null

      if (certificateType === 'ID') {
        // 中国居民身份证
        findSignCountry = 'CN'
      } else if (certificateType === 'HT') {
        // 香港身份证
        findSignCountry = 'HK'
      } else if (certificateType === 'MD') {
        // 澳门身份证
        findSignCountry = 'MO'
      } else if (certificateType === 'TD') {
        // 台湾身份证
        findSignCountry = 'TW'
      } else {
        return countryList || []
      }

      const findItem = countryList.find(item => item.name === findSignCountry)
      return findItem ? [findItem] : countryList
    }
  },
  created () {
    const { optType, personCertType } = this
    const { id } = this.currentCertItem

    this.getRelatedName()
    // 区分主证件 or 辅助证件
    this.formData.mainDocument = personCertType === 'main'
    if (optType === 'update') {
      if (!id) {
        // 如果是更新并且没有审核数据，则去拿原有数据的部分信息
        const list = ['relatedNameEn', 'certificateType', 'signCountry']
        list.map(item => {
          this.formData[item] = this.currentCertItem[item]
        })
      } else {
        this.getCertCheckDetail()
      }
    } else if (optType === 'view' && id) {
      // 如果是查看，并且有id时，则去拿审核信息
      this.getCertCheckDetail()
    }
  },
  methods: {
    // 重新上传
    reloadUpdate () {
      this.$emit('reloadUpdate', this.currentCertItem)
    },
    // 修改证件类型
    changeCertType () {
      const { certificateType } = this.formData

      if (certificateType === 'ID') {
        // 中国居民身份证 - 签发国家直接带出中国
        this.formData.signCountry = 'CN'
      } else if (certificateType === 'HT') {
        // 香港身份证 - 签发国家直接带出香港
        this.formData.signCountry = 'HK'
      } else if (certificateType === 'MD') {
        // 澳门身份证 - 签发国家直接带出澳门
        this.formData.signCountry = 'MO'
      } else if (certificateType === 'TD') {
        // 台湾身份证 - 签发国家直接带出台湾
        this.formData.signCountry = 'TW'
      } else {
        this.formData.signCountry = ''
      }
    },
    // 获取详情
    getCertCheckDetail () {
      const { optType } = this
      const { cstNo, id } = this.currentCertItem

      api.getCertCheckDetail({
        cstNo, id
      }).then(res => {
        if (res) {
          this.detailInfo = res || {}
          this.formData = res || {}
          // 如果失效日期为长期有效，则勾选长期有效
          if (res.expirationDate && res.expirationDate === '2099-12-31') {
            this.expirationDateLongChecked = true
          }

          // 如果是更新 - 因为证件号码为脱敏，需要过滤掉证件号，否则更新会有问题
          if (optType === 'update') {
            this.formData.certificateNo = ''
          }

          // 处理证件回显
          const { certificateType, certificateFront, certificateBack, certificatePath, fileName, fileNameFront, fileNameBack } = this.detailInfo

          if (certificateType === 'ID') {
            this.cardFrontList = [{
              fileNo: certificateFront,
              fileName: fileNameFront,
              name: fileNameFront,
              path: certificateFront
            }]
            this.cardBackList = [{
              fileNo: certificateBack,
              fileName: fileNameBack,
              name: fileNameBack,
              path: certificateBack
            }]
          } else {
            this.fileList = [{
              fileNo: certificatePath,
              fileName,
              name: fileName,
              path: certificatePath
            }]
          }
        }
      })
    },
    // 获取相关人
    getRelatedName () {
      api.getRelatedName().then(res => {
        if (res) {
          const { nameEn } = res

          this.nameList = nameEn || []
        }
      })
    },
    handleChangeBox (val) {
      if (val) {
        this.formData.expirationDate = '2099-12-31'
      } else {
        this.formData.expirationDate = ''
      }
    },
    // 确认上传
    confirmUpload () {
      this.$refs.form.validate(valide => {
        if (valide) {
          const { optType, personCertType, fileList, cardFrontList, cardBackList, formData, currentCertItem } = this
          const { certificateType, certificateNo, effectiveDate, expirationDate } = formData
          const { certificateInfoId, id, checkStatus } = currentCertItem
          const list = []

          if (expirationDate && effectiveDate && new Date(effectiveDate) > new Date(expirationDate)) {
            // 生效日期、失效日期存在 并且 失效日期不可小于生效日期
            this.$warn('失效日期不可小于生效日期')
            return
          }

          if (certificateType === 'ID') {
            // 如果是中国居民身份证
            if (cardFrontList.length <= 0 || cardBackList.length <= 0) {
              this.$warn('请上传证件')
              return false
            }
            if (certificateNo && certificateNo.length !== 18 && certificateNo.length !== 15) {
              // 校验身份证号长度
              this.$warn('证件号有误，请检查')
              return false
            }
            // 处理正反面
            cardFrontList.map(item => {
              const { name: fileName, path: certificateFront } = item
              const data = {
                ...formData,
                fileName,
                certificatePath: certificateFront,
                certificateFront,
                // 审核拒绝才传id
                id: checkStatus === 'AUDIT_REJECT' ? id : '',
                certificateInfoId,
                dataType: 'PERSONAL'
              }
              // 正面删除反面的信息
              delete data.certificateBack
              list.push(data)
            })

            cardBackList.map(item => {
              const { name: fileName, path: certificateBack } = item
              const data = {
                ...formData,
                fileName,
                certificatePath: certificateBack,
                certificateBack,
                // 审核拒绝才传id
                id: checkStatus === 'AUDIT_REJECT' ? id : '',
                certificateInfoId,
                dataType: 'PERSONAL'
              }
              // 反面删除正面的信息
              delete data.certificateFront
              list.push(data)
            })
          } else {
            if (!fileList || fileList.length <= 0) {
              this.$warn('请上传证件')
              return false
            }

            fileList.map(item => {
              const { name: fileName, path: certificatePath } = item
              const data = {
                ...formData,
                fileName,
                certificatePath,
                certificateInfoId,
                // 审核拒绝才传id
                id: checkStatus === 'AUDIT_REJECT' ? id : '',
                dataType: 'PERSONAL'
              }
              // 删除身份证的信息 - 假如原来是身份证，更新时变更了证件类型，则把身份证的信息抹除掉
              delete data.certificateBack
              delete data.certificateFront
              list.push(data)
            })
          }

          api.saveCertCheck({
            list
          }).then(res => {
            if (res) {
              this.$emit('uploadSucess', {
                optType,
                personCertType,
                type: 'person'
              })
            }
          })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
  .certificate-manage-dialog {
    font-family: PingFangSC-Light, PingFang SC;
    .form-row {
      display: flex;
      .form-item-col {
        flex-shrink: 1;
      }
    }

    .expiration-date-container {
      position: relative;
      .check-container {
        position: absolute;
        right: 0;
        line-height: 12px;
        padding-top: 4px;
      }
    }
    .audit-result-info {
      width: 450px;
      padding: 20px;
      min-height: 52px;
      border-radius: 2px;
      border: 1px dashed #D7DBEC;
      margin-top: 30px;

      &.audit-pass .audit-info-item p {
        color: #27CBA4
      }

      &.audit-fail .audit-info-item p {
        color: #F34359;
      }

      .audit-info-item {
        display: flex;
        font-size: 14px;
        font-weight: 300;
        margin-bottom: 20px;

        &:nth-last-child(1) {
          margin-bottom: 0;
        }

        label {
          color: #333752;
          width: 86px;
          margin-right: 4px;
        }

        p {
          color: #333752;
          flex: 1;
          word-break: break-all;
          line-height: 18px;
        }
      }
    }

    .dialog-footer {
      padding-right: 65px;
    }

    .id-card-list {
      display: flex;

      .id-card-item {
        margin-right: 20px;

        p {
          font-size: 14px;
          font-weight: 300;
          color: #333752;
          margin-bottom: 15px;
        }
      }
    }

    .upload-tips {
      margin-top: 20px;
      color: #A1A7C4;
      font-size: 12px;
      line-height: 18px;

      span {
        color: #F34359;
        margin-right: 6px;
      }
    }
  }
</style>
