<template>
  <div class="container">
    <div class="notice" v-if="showUpdateNotice">
      <Notice
        @close="handleCloseNotice"
        notice="卓越理财2.0全新升级：新增欧元、离岸人民币及港币理财，助您把握全球市场机遇！灵活提取，分散风险，优化收益，并利用汇率对冲实现额外收益，轻松实现资产保值增值！"
      />
    </div>
    <ModuleSection title="产品详情" class="product-container">
      <div class="product-wrapper" v-loading="dataLoding">
        <div class="currency-info-container">
          <!-- 轮播图 -->
          <div class="swiper-container">
            <div class="swiper-wrapper currency-info-list">
              <div
                class="swiper-slide currency-info-item"
                v-for="(item, index) in currencyInfoList"
                :key="item.currency"
                :class="{ active: index === selectCurrencyIndex }"
              >
                <div class="item-title">
                  <div class="img">
                    <img :src="currencyUrl[item.currency]" alt="" />
                  </div>
                  <div>
                    {{ lang == "en" ? item.fundNameEn : item.fundName }}
                  </div>
                </div>
                <div class="item-amount">
                  <div>{{ $t("持有") }}</div>
                  <div class="amount">
                    {{ $_format.toCashWithComma(item.totalAccAmount || 0) }}
                  </div>
                </div>
              </div>
            </div>
            <!-- 如果需要分页器 -->
            <div class="swiper-pagination"></div>
          </div>
          <!-- 如果需要导航按钮 -->
          <div class="swiper-button-prev"></div>
          <div class="swiper-button-next"></div>
        </div>
        <div class="sub-container">
          <div class="left-section">
            <ul class="info-list">
              <li class="info">
                <label class="label">{{ $t("年化收益") }}</label>
                <span class="value-unit">
                  <span class="value">{{ annualEarnings }}</span>
                  <!-- <span class="value">{{ fundData.description && fundData.description.includes(';') && fundData.description.split(';').length > 0 ? fundData.description.split(';')[0] : fundData.description }}</span> -->
                  <span class="unit">%</span>
                </span>
              </li>
              <li class="info">
                <label class="label">{{ $t("产品期限") }}</label>
                <span class="value-unit">
                  <span class="value">{{ $t("活期") }}</span>
                </span>
              </li>
              <li class="info">
                <label class="label">{{ $t("起投金额") }}</label>
                <span class="value-unit">
                  <span class="value">{{ fundData.minAmount || 0 }}</span>
                  <span class="unit">{{ fundData.currencyType }}</span>
                </span>
              </li>
            </ul>
            <div class="prodect-intro">
              <div class="title">{{ $t("产品特点") }}</div>
              <ul class="intro-list">
                <li class="intro-item">
                  <div class="img">
                    <img
                      src="@/assets/image/fund/icon-excellence-01.png"
                      alt=""
                    />
                  </div>
                  <div>
                    {{ $t("申赎方式：无节假日限制，随存随取") }}
                  </div>
                </li>
                <li class="intro-item">
                  <div class="img">
                    <img
                      src="@/assets/image/fund/icon-excellence-02.png"
                      alt=""
                    />
                  </div>
                  <div>
                    {{ $t("收益情况：收益稳健，无节假日限制") }}
                  </div>
                </li>
                <li class="intro-item">
                  <div class="img">
                    <img
                      src="@/assets/image/fund/icon-excellence-03.png"
                      alt=""
                    />
                  </div>
                  <div>
                    <i18n tag="span" path="investmentThreshold">
                      <template v-slot:minAmount>
                        {{ fundData.minAmount || 0 }}
                      </template>
                      <template v-slot:incrAmount>
                        {{ fundData.incrAmount || 0 }}
                      </template>
                      <template v-slot:currency>
                        {{ fundData.currencyType }}
                      </template>
                    </i18n>
                  </div>
                </li>
                <li class="intro-item">
                  <div class="img">
                    <img
                      src="@/assets/image/fund/icon-excellence-04.png"
                      alt=""
                    />
                  </div>
                  <div>
                    {{ $t("相关费用：0管理费，0认购费") }}
                  </div>
                </li>
              </ul>
            </div>
          </div>
          <div class="right-section">
            <div class="info-card">
              <div class="info-card-container">
                <div class="info-card-header">
                  <!-- 有产品公告时才会展示该按钮 -->
                  <p
                    v-if="fundData.noticeContent"
                    class="new-notice"
                    @click="showNotice()"
                  >
                    {{ $t("产品公告") }}
                  </p>
                  <p
                    class="financial-details"
                    @click="jumpFinancialDetailPage()"
                    >{{ $t("收益明细") }}</p
                  >
                  <p
                    class="financial-details more-setting"
                    @click="excellenceMoreSetting"
                  >
                    {{ $t("更多设置") }}
                  </p>
                </div>

                <div class="total-amount">
                  <span class="label">{{ $t("持有资产") }}</span>
                  <span class="value">
                    {{
                      $_format.toCashWithComma(accountData.totalAccAmount || 0)
                    }}</span
                  >
                  <span class="unit">{{ $t(fundData.currencyType) }}</span>
                </div>
                <ul class="account-amount">
                  <li class="amount">
                    <label class="label">{{ $t("累计收益") }}</label>
                    <span class="value-unit">
                      <span class="value">{{
                        $_format.toCashWithComma(
                          accountData.accumulatedIncomeAmount || 0
                        )
                      }}</span>
                      <span class="unit">{{ $t(fundData.currencyType) }}</span>
                    </span>
                  </li>
                  <li class="amount">
                    <label class="label">{{ $t("昨日收益") }}</label>
                    <span class="value-unit">
                      <span class="value">{{
                        $_format.toCashWithComma(
                          accountData.yesterdayIncomeAmount || 0
                        )
                      }}</span>
                      <span class="unit">{{ $t(fundData.currencyType) }}</span>
                    </span>
                  </li>
                </ul>
                <div class="btn-list">
                  <el-button
                    :disabled="disabledTransferOut"
                    class="outward-btn"
                    @click="outward()"
                    >{{ $t("转出") }}</el-button
                  >
                  <!-- 资金不足起投金额及追加金额，“确定划转”按钮置灰 -->
                  <el-button
                    :loading="loading"
                    class="inward-btn"
                    @click="inward()"
                    type="primary"
                    >{{ $t("转入") }}</el-button
                  >
                </div>
              </div>
              <div
                class="info-container"
                v-if="accountData.showMsg || accountData.showMsgEn"
              >
                {{ lang == "en" ? accountData.showMsgEn : accountData.showMsg }}
              </div>
              <!-- 当地银行时间 -->
              <div class="current-time">
                {{ $t("银行当地交易时间") }}（UTC-4）{{ serverDateTime }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="tabs mt-40">
        <div
          v-for="(item, index) in tabLists"
          :key="index"
          :class="['tab', { active: activeId === item.id }]"
          @click="handleToogleTabs(item)"
        >
          {{ $t(item.label) }}
        </div>
      </div>
      <div class="intro-container">
        <production-intro
          :productIntroduce="fundData.proIntroductFileNo"
          :proIntroduct="fundData.proIntroduct"
          v-show="activeId === 1"
        />
        <protocol
          :product-files="fundData.proFilesList"
          :sign-an-agreement="fundData.proAgreementList"
          v-show="activeId !== 1"
        />
      </div>
      <div
        style="
          color: #4a6aff;
          font-size: 12px;
          text-align: center;
          margin-top: 38px;
        "
      >
        ～ {{ $t("理财非存款，产品有风险，投资需谨慎") }} ～
      </div>

      <!-- 购买提示弹窗 -->
      <PuchaseTipsDialog
        :visible="puchaseTipsVisible"
        :confirmLoading="puchaseTipsLoading"
        @close="puchaseTipsVisible = false"
        @confirm="handleConfirmPuchaseTip"
      />

      <LimitErrorDialog
        :visible="limitErrorDialogVisible"
        :close-on-press-escape="false"
        :close-on-click-modal="false"
        @close="limitErrorDialogVisible = false"
      />

      <!-- 询证函 -->
      <FundConfirmationDialog
        :visible="fundConfirmationDialogVisible"
        :close-on-press-escape="false"
        :close-on-click-modal="false"
        @close="fundConfirmationDialogVisible = false"
      />
    </ModuleSection>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import ModuleSection from '@/components/container/ModuleSection'
import productionIntro from '../../yaobaoDetail/components/productionIntro'
import protocol from '../../yaobaoDetail/components/protocol'
import homeApi from '@/views/home/<USER>'
import {
  comFileDownload,
  queryCompanyUserOpenStatus,
  addPopupWindows
} from '@/views/fund/api'
import excellenceApi from '../../api/excellence'
import { checkComCurrAccount } from '../../api/yaobao'

import Notice from '@/components/common/Notice.vue'

import 'swiper/swiper-bundle.css'
import { Swiper, Navigation, Pagination } from 'swiper'

import { cion } from '@/utils/coin/index.js'

Swiper.use([Navigation, Pagination])
export default {
  components: {
    ModuleSection,
    productionIntro,
    protocol,
    PuchaseTipsDialog: () =>
      import('@/views/fund/components/PuchaseTipsDialog'),
    LimitErrorDialog: () => import('@/views/fund/components/LimitErrorDialog'),
    FundConfirmationDialog: () =>
      import('@/views/fund/components/FundConfirmationDialog'),
    Notice
  },
  data () {
    return {
      showUpdateNotice: false,
      imageMap: cion,
      currencyUrl: {},
      tabLists: [
        { label: '产品介绍', id: 1 },
        { label: '相关协议', id: 2 }
      ],
      activeId: 1,
      selectCurrencyIndex: 0,
      selectCurrency: '',
      currencyInfoList: [],
      fundData: {},
      accountData: {},
      fundDetailLoading: false,
      accountDataLoading: false,
      // 时间
      serverTimer: null, // 定时器
      year: '',
      month: '',
      day: '',
      hour: '',
      minute: '',
      serverDateTime: '',

      loading: false,
      puchaseTipsLoading: false,
      limitErrorDialogVisible: false,
      puchaseTipsVisible: false,
      fundConfirmationDialogVisible: false,
      swiper: null
    }
  },
  beforeDestroy () {
    this.serverTimer && clearInterval(this.serverTimer)
  },
  async created () {
    this.getNotify()
    this.productListT0()
    // 获取银行时间
    this.getServerDateTime()
    // 先判断是否开通理财户
    const { loginInfo } = this
    // 未开通
    // 再次去检查是否开户
    const { customerId: cstNo } = loginInfo
    const res = await queryCompanyUserOpenStatus({
      cstNo
    }).catch((error) => console.error(error))
    const { openStatus, asyncItemList } = res
    if (openStatus === 1) {
      // 已开户 - 存储到store中
      this.setFundOpenStatus(openStatus) // 企业理财开户结果
      this.setExcellenceOpenStatusList(asyncItemList) // 卓越理财开户结果，与对应币种代扣服务
    }
    // else {
    //   this.$tconfirm('请先完成开通理财账户', '', {
    //     confirmButtonText: '前去开户'
    //   }).then(() => {
    //     this.$router.push({
    //       name: 'FundAccountOpening'
    //     })
    //   })
    // }
  },
  mounted () {
    this.setSeiper()
  },
  methods: {
    ...mapMutations('fund', [
      'setFundOpenStatus',
      'setExcellenceOpenStatusList'
    ]),
    ...mapMutations('app', ['SET_TIME_INFO']),
    setSeiper () {
      const _this = this
      // eslint-disable-next-line no-new
      this.swiper = new Swiper('.swiper-container', {
        allowTouchMove: true, // 禁止触摸滑动
        slidesPerView: 4, // 设置slider容器能够同时显示的slides数量
        spaceBetween: 21, // slide之间的距离（单位px）
        observer: true, // 修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, // 修改swiper的父元素时，自动初始化swiper
        shortSwipes: false,
        loop: false,
        // 如果需要前进后退按钮
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        },
        // 如果需要分页器
        // pagination: {
        //   el: '.swiper-pagination',
        //   clickable: true
        // },
        // swiper点击事件
        on: {
          tap: (e) => {
            _this.selectCurrencyIndex = e.clickedIndex
            _this.selectCurrency =
              _this.currencyInfoList[_this.selectCurrencyIndex].currency
            _this.getFundDetails()
          }
        }
      })
    },
    handleCloseNotice () {
      this.showUpdateNotice = false
    },
    getNotify () {
      const { customerId: cstNo } = this.loginInfo
      excellenceApi
        .addUpdateNotify({ cstNo })
        .then((res) => {
          this.showUpdateNotice = res.result || false
        })
        .catch(console.log)
    },
    // 购买弹窗确认
    handleConfirmPuchaseTip () {
      this.puchaseTipsLoading = true
      addPopupWindows({
        openStatus: 1 // 开启-1/关闭-0
      })
        .then((res) => {
          this.puchaseTipsLoading = false
          this.puchaseTipsVisible = false

          // 继续做汇入操作
          this.inward()
        })
        .catch((err) => {
          console.error(err)
          this.puchaseTipsLoading = false
        })
    },
    productListT0 () {
      const { customerId: cstNo } = this.loginInfo
      excellenceApi
        .productListT0({
          cstNo
        })
        .then((res) => {
          this.currencyInfoList = res.map((item) => item)

          const { currency } = this.$route.query
          if (currency) {
            this.selectCurrencyIndex = this.currencyInfoList.findIndex(
              (item) => item.currency === currency
            )
            this.selectCurrency = currency
          } else {
            this.selectCurrency =
              this.currencyInfoList[this.selectCurrencyIndex].currency
          }
          // if (currency) {
          //   this.selectCurrency = currency
          // } else {
          //   this.selectCurrency = 'USD'
          // }
          // this.selectCurrencyIndex = this.currencyInfoList.findIndex(
          //   (item) => item.currency === this.selectCurrency
          // )

          this.getFundDetails()

          this.currencyInfoList.forEach((item) => {
            this.downloadLogoImg(item)
          })
        })
        .catch(console.log)
    },
    // 获取活期理财详情
    getFundDetails () {
      this.fundDetailLoading = true
      const { customerId: cstNo } = this.loginInfo
      const { code: fundCode } =
        this.currencyInfoList[this.selectCurrencyIndex]
      excellenceApi
        .getFundDetails({
          cstNo,
          fundCode
        })
        .then((res) => {
          this.fundDetailLoading = false
          if (res) {
            const { noticeContent, fundCode, popupFlag, currencyType } = res
            this.fundData = res
            this.getFundAccountDetails()

            // 首次才会主动弹出公告
            const fundCodeSessionStorage = window.sessionStorage.getItem(
              `fundCodeNoticeFlag${fundCode}${currencyType}`
            )

            if (noticeContent && !fundCodeSessionStorage) {
              this.showNotice()
              window.sessionStorage.setItem(
                `fundCodeNoticeFlag${fundCode}`,
                fundCode
              )
            }

            this.fundConfirmationDialogVisible = popupFlag
          }
        })
        .catch((_) => {
          this.fundDetailLoading = false
          this.accountDataLoading = false
        })
    },
    // 获取账户信息
    getFundAccountDetails () {
      const { fundCode } = this.fundData
      const { customerId: cstNo } = this.loginInfo

      this.accountDataLoading = true
      // 先去校验账户
      checkComCurrAccount({
        cstNo,
        productCode: fundCode
      })
        .then(() => {
          excellenceApi
            .getFundAccountDetails({
              cstNo,
              fundCode
            })
            .then((res) => {
              this.accountDataLoading = false
              if (res) {
                this.accountData = res || {}
              }
            })
            .catch((_) => {
              this.accountDataLoading = false
            })
        })
        .catch((error) => {
          if (['C0008', 'C0009', 'C0010'].includes(error.retCode)) {
            this.$talert('完成开户后可查看产品详情', '', {
              confirmButtonText: '前去开户'
            }).then(() => {
              this.$router.push({
                name: 'FundAccountOpening'
              })
            })
          } else if (['C0015'].includes(error.retCode)) {
            this.accountData = {}
          }
          this.accountDataLoading = false
        })
    },
    // 获取银行时间
    getServerDateTime () {
      homeApi
        .getServerDateTime()
        .then((res) => {
          if (res) {
            const { dateTime } = res
            const year = dateTime.substring(0, 4)
            const month = dateTime.substring(4, 6)
            const day = dateTime.substring(6, 8)
            const hour = dateTime.substring(8, 10)
            const minute = dateTime.substring(10, 12)
            const seconds = dateTime.substring(12, 14)
            const date = `${year}-${month}-${day}`
            const time = `${hour}:${minute}:${seconds}`
            // date存储到vuex中
            this.SET_TIME_INFO({ date })
            this.serverDateTime = `${date} ${time}`
            // 注意safari浏览器下的兼容格式
            this.serverStamp = +new Date(`${year}/${month}/${day} ${time}`)
            // 创建定时器
            this.serverTimer = setInterval(() => {
              this.serverStamp += 1000
              this.serverDateTime = new Date(this.serverStamp).format(
                'yyyy-mm-dd hh:mi:ss'
              )
            }, 1000)
          }
        })
        .catch((err) => {
          console.error(err)
        })
    },
    // 展示公告详情
    showNotice () {
      const { noticeContent } = this.fundData
      this.$talert(noticeContent, '卓越理财公告', {
        confirmButtonText: '我已知晓'
      }).then(() => {})
    },
    // 收益明细
    jumpFinancialDetailPage () {
      const { fundCode } = this.fundData
      this.$router.push({
        name: 'ExcellenceIncomeDetail',
        query: {
          fundCode
        }
      })
    },
    getHitFiveEyes () {
      this.$talert('抱歉，您暂时无法购买理财产品，现有持仓不受影响', '提示', {
        confirmButtonText: '确定'
      })
        .then((_) => {})
        .catch((_) => {})
    },
    // 更多设置
    excellenceMoreSetting () {
      const { fundCode, currencyType: currency } = this.fundData
      const { hitFiveEyes } = this
      if (hitFiveEyes) {
        this.getHitFiveEyes()
        return
      }
      if (!this.checkFundOpenStatus()) return
      if (!this.checkFundAccountType()) return
      this.$router.push({
        name: 'ExcellenceSetting',
        query: {
          currency,
          fundCode
        }
      })
    },
    checkFundAccountType () {
      const { currProductCode } = this.accountData
      if (!currProductCode) {
        this.$talert('您还未开通对应币种账户，请先去开通', '', {
          confirmButtonText: '前去开户'
        }).then(() => {
          this.$router.push({
            name: 'ElectronicAccountOpening'
          })
        })
        return false
      }
      return true
    },
    checkFundOpenStatus () {
      // 先判断是否开通理财户
      const { fundOpenStatus } = this
      if (fundOpenStatus !== 1) {
        // 未开通
        this.$tconfirm('请先完成开通理财账户', '', {
          confirmButtonText: '前去开户'
        }).then(() => {
          this.$router.push({
            name: 'FundAccountOpening'
          })
        })
        return false
      }
      return true
    },
    outward () {
      if (!this.checkFundOpenStatus()) return
      if (!this.checkFundAccountType()) return

      // const returnFlag = true
      // if (returnFlag) {
      //   this.$talert(
      //     `<div style="white-space: pre-line; line-height: 24px;">
      //       ${this.$t('因系统升级维护，卓越理财赎回暂停，\n预计恢复时间2025-07-01 4:00(UTC-4)')}
      //     </div>`,
      //     '卓越理财公告',
      //     {
      //       dangerouslyUseHTMLString: true
      //     }
      //   )
      //   return
      // }

      const {
        fundCode,
        fundName,
        fundNameEn,
        currencyType: currency
      } = this.fundData
      const { excellenceOpenStatusList } = this
      const fundItem = excellenceOpenStatusList.find(
        (item) => item.cry === currency
      )
      if (!fundItem) {
        this.$router.push({
          name: 'ElectronicAccountOpeningSubmit'
        })
        return
      }
      this.$router.push({
        name: 'ExcellenceTransfer',
        query: {
          fundCode,
          fundName,
          fundNameEn,
          currency,
          type: 'transferOut'
        }
      })
    },
    async inward () {
      if (!this.checkFundOpenStatus()) return
      if (!this.checkFundAccountType()) return

      const {
        fundCode,
        fundName,
        fundNameEn,
        currencyType: currency
      } = this.fundData
      const { excellenceOpenStatusList } = this
      const fundItem = excellenceOpenStatusList.find(
        (item) => item.cry === currency
      )
      if (!fundItem) {
        this.$router.push({
          name: 'ElectronicAccountOpeningSubmit'
        })
        return
      }
      // 转入校验
      this.loading = true
      const checkRes = await excellenceApi
        .comBuyT0Check({
          fundCode
        })
        .catch((_) => {
          const { retCode } = _
          if (retCode === 'T999') {
            this.limitErrorDialogVisible = true
          } else if (retCode === 'T998') {
            this.puchaseTipsVisible = true
          }
          this.loading = false
        })

      this.loading = false
      if (!checkRes) return

      this.$router.push({
        name: 'ExcellenceTransfer',
        query: {
          fundCode,
          fundName,
          fundNameEn,
          currency,
          type: 'transferIn'
        }
      })
    },
    // 切换tab
    handleToogleTabs (item) {
      this.activeId = item.id
    },
    downloadLogoImg (item) {
      const { loginInfo } = this
      const { customerId: cstNo } = loginInfo
      const { logoFileNo, logoFileName, currency } = item
      const windowMimes = {
        'image/jpg': /\.jpg$/,
        'image/jpeg': /\.jpeg$/,
        'image/png': /\.png$/
      }
      let type
      for (const k in windowMimes) {
        if (windowMimes[k].test(logoFileName.toLowerCase())) {
          type = k
        }
      }
      comFileDownload({
        fileNo: logoFileNo,
        cstNo
      }).then((res) => {
        const file = new Blob([res], { type })
        this.currencyUrl[currency] = URL.createObjectURL(file)
      })
    }
  },
  computed: {
    ...mapState('login', ['loginInfo']),
    ...mapState('account', ['accountMap']),
    ...mapState('home', ['sessionData']),
    ...mapState('fund', [
      'fundOpenStatus',
      'excellenceOpenStatusList',
      'hitFiveEyes'
    ]),
    ...mapState('app', ['lang']),

    disabledTransferOut () {
      const { totalAccAmount } = this.accountData
      if (
        !totalAccAmount ||
        !Number(totalAccAmount) ||
        Number(totalAccAmount) <= 0
      ) {
        return true
      }
      return false
    },
    // 银行时间
    serviceIitem () {
      const { year, month, day, hour, minute } = this
      const zh = `${year}${this.$t('年')}${month}${this.$t(
        '月'
      )}${day}${this.$t('日')}  ${hour}:${minute}`
      const en = `${year}-${month}-${day}  ${hour}:${minute}`
      return this.lang === 'en' ? en : zh
    },
    // 加载中判断
    dataLoding () {
      const { accountDataLoading, fundDetailLoading } = this

      if (fundDetailLoading || accountDataLoading) return true
      return false
    },
    // 年化收益
    annualEarnings () {
      const { description } = this.fundData
      if (description) {
        if (description.includes(';') && description.split(';').length) {
          return description.split(';')[0]
        } else if (
          description.includes('；') &&
          description.split('；').length
        ) {
          return description.split('；')[0]
        }
        return description
      } else {
        return description
      }
    }
  }
}
</script>

<style lang="less" scoped>
.mt-10 {
  margin-top: 10px;
}
.mt-30 {
  margin-top: 30px;
}
.mt-40 {
  margin-top: 40px;
}
.container {
  background: @blueLess5;
  .notice {
    margin-bottom: 20px;
  }
  .product-container {
    background: @white;
    .product-wrapper {
      border-radius: 10px;
      background-color: @grayLess10;
      padding-top: 40px;
      .currency-info-container {
        position: relative;
        width: 100%;
        padding: 0 40px;
        .swiper-container {
          padding-bottom: 5px;
        }
        .swiper-button-prev,
        .swiper-button-next {
          width: 24px;
          height: 100%;
          margin-top: 0;
          background-color: @grayLess10;
          &.swiper-button-disabled {
            &::after {
              opacity: 0;
            }
          }
          &:hover {
            border-radius: 4px;
            background-color: @grayLess20;
          }
          &::after {
            content: "";
            width: 24px;
            height: 24px;
            background-image: url("~@/assets/image/icons/icon-arrow-right-gray.png");
            background-position: center;
            background-size: 100% 100%;
            background-repeat: no-repeat;
          }
        }
        .swiper-button-prev {
          top: 0;
          left: 8px;
          transform: rotate(180deg);
        }
        .swiper-button-next {
          top: 0;
          right: 8px;
        }
        .currency-info-list {
          .currency-info-item {
            padding: 16px 16px 16px 20px;
            border: 1px solid @grayLess20;
            border-radius: 4px;
            background: @white;
            cursor: pointer;
            &.active {
              border-color: @blue;
              background-color: #f6f7ff;
            }
            .item-title {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              font-size: 14px;
              color: @black;
              line-height: 14px;
              .img {
                width: 24px;
                height: 24px;
                margin-right: 10px;
                img {
                  width: 100%;
                }
              }
            }
            .item-amount {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              margin-top: 10px;
              font-size: 14px;
              color: @black;
              line-height: 14px;
              .amount {
                font-size: 18px;
                color: @blackMore;
                line-height: 24px;
                margin-left: 6px;
              }
            }
          }
        }
      }
      .sub-container {
        padding: 35px 40px 40px;
        border-radius: 10px;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        .left-section {
          flex: 1;
          .production-title {
            font-size: 16px;
            font-weight: 400;
            color: @blackMore;
          }
          .info-list {
            display: flex;
            background-color: @white;
            padding: 20px 30px;
            border-radius: 10px;
            .info {
              display: flex;
              flex-direction: column;
              margin-right: 70px;
              &:nth-last-child(1) {
                margin-right: 0;
              }
              .label {
                color: @black;
                font-size: 14px;
              }
              .value-unit {
                display: flex;
                align-items: flex-end;
                margin-top: 20px;
                .value {
                  font-size: 24px;
                  color: @red;
                }
                .unit {
                  font-size: 14px;
                  color: @black;
                  margin-left: 10px;
                  line-height: 20px;
                }
              }
            }
          }
          .sub-title {
            font-size: 16px;
            color: @black;
            font-weight: 300;
            position: relative;
            &::before {
              content: "";
              display: inline-block;
              position: absolute;
              left: -10px;
              top: 5px;
              width: 5px;
              height: 5px;
              border-radius: 5px;
              background-color: @grayLess40;
            }
          }
          // .production-character {
          //   margin-top: 20px;
          //   .character-detail {
          //     line-height: 22px;
          //     font-size: 14px;
          //     font-weight: 300;
          //     color: @black;
          //     margin-bottom: 20px;
          //   }
          // }
          .prodect-intro {
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.6);
            overflow: hidden;
            margin-top: 30px;
            .title {
              font-size: 16px;
              color: @blackMore;
              line-height: 16px;
              padding: 17px 20px;
              background: @grayLess20;
            }
            .intro-list {
              padding: 20px 30px 30px;
              .intro-item {
                display: flex;
                align-items: center;
                font-size: 14px;
                color: @black;
                line-height: 14px;

                & + .intro-item {
                  margin-top: 20px;
                }
                .img {
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  flex-shrink: 0;
                  width: 30px;
                  height: 30px;
                  margin-right: 10px;
                  background: @grayLess20;
                  border-radius: 4px 4px 4px 4px;
                  img {
                    width: 20px;
                    height: 20px;
                  }
                }
              }
            }
          }

          .chart-tabs {
            width: 380px;
            height: 36px;
            display: flex;
            .tab {
              padding-bottom: 21px;
              cursor: pointer;
              color: @black;
              font-size: 14px;
              border-bottom: 1px solid @grayLess20;
              flex: 1;
            }
            .active {
              color: @blue;
              border-color: @blue;
            }
          }
        }
        .right-section {
          padding-top: 10px;
          .info-card {
            width: 400px;
            min-height: 397px;
            padding: 40px 32px 0;
            box-sizing: border-box;
            background: @white;
            border-radius: 10px;
            margin-left: 20px;
            position: relative;

            .info-card-container {
            }

            .info-card-header {
              display: flex;
              align-items: center;
              justify-content: flex-end;
              min-width: 240px;
              padding-left: 40px;
              padding-right: 20px;
              height: 45px;
              background: url("../../../../assets/image/fund/fund-btn-back.png");
              background-size: 100% 100%;
              border-radius: 100px 10px 0px 0px;
              position: absolute;
              right: 0;
              top: -10px;
              z-index: 2;

              .financial-details {
                min-width: 70px;
                min-height: 26px;
                font-size: 14px;
                background: @blueLess10;
                border-color: @blueLess10;
                color: @blue;
                text-align: center;
                padding: 5px 7px;
                border-radius: 4px;
                font-weight: 300;
                cursor: pointer;
                &:hover {
                  color: @blue;
                  background: @blueLess20;
                  border-color: @blueLess20;
                  box-shadow: 0px 6px 12px 0px @blueLess10;
                }
              }
              .more-setting {
                margin-left: 8px;
              }

              .new-notice {
                color: @blue;
                cursor: pointer;
                text-align: center;
                font-size: 14px;
                min-width: 70px;
                padding: 5px 7px;
                border: 1px solid #ecf0ff;
                border-radius: 4px;
                margin-right: 8px;

                &:hover {
                  border-color: @blue;
                }
              }
            }

            .total-amount {
              margin-top: 20px;
              display: flex;
              .label {
                color: @black;
                font-size: 14px;
                margin-top: 5px;
              }
              .value {
                color: @blackMore;
                font-size: 24px;
                font-weight: 400;
                margin-left: 10px;
              }
              .unit {
                color: @black;
                font-size: 14px;
                margin-left: 5px;
                align-self: flex-end;
              }
            }
            .account-amount {
              display: flex;
              margin-top: 30px;
              .amount {
                display: flex;
                flex-direction: column;
                flex: 1;
                .label {
                  color: @black;
                  font-size: 14px;
                }
                .value-unit {
                  margin-top: 20px;
                  .value {
                    font-size: 20px;
                    color: @blackMore;
                  }
                  .unit {
                    margin-left: 5px;
                    font-size: 14px;
                    color: @black;
                  }
                }
              }
            }
            .btn-list {
              display: flex;
              margin-top: 40px;
              .outward-btn {
                width: 100%;
              }
              .inward-btn {
                margin-left: 20px;
                width: 100%;
              }
            }
            .info-container {
              padding: 10px;
              font-size: 14px;
              color: @blackMore;
              line-height: 20px;
              margin-top: 20px;
              background: @grayLess10;
              border-radius: 4px 4px 4px 4px;
            }
            .current-time {
              width: 100%;
              height: 54px;
              border-radius: 10px;
              display: flex;
              align-items: center;
              color: @blackLess;
              font-size: 14px;
            }
          }
        }
      }
    }
    .intro-container {
      padding: 20px;
      background-color: @grayLess10;
      border-radius: 10px;
    }
    .tabs {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 40px;
      overflow: hidden;
      cursor: pointer;

      .tab {
        display: flex;
        justify-content: center;
        align-items: center;
        min-width: 172px;
        padding: 0 20px;
        height: 54px;
        color: @blackLess;
        background-color: @grayLess10;
        border: 1px solid @grayLess10;
        border-radius: 5px;
        &:hover {
          color: @blue;
        }
        &:first-child {
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
        }
        &:last-child {
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
        }
        &.active {
          color: @blue;
          background-color: @white;
          border-color: @grayLess20;
          box-shadow: 0px 4px 10px 0px rgba(51, 55, 82, 0.06);
        }
      }
    }
  }
}
</style>
