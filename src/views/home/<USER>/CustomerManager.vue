<template>
  <div
    v-if="
      Object.keys(customerManagerInfo).length &&
      ((customerManagerInfo.directorName && lang === 'zh-CN') ||
      (customerManagerInfo.directorNameEn && lang === 'en'))
    "
    :class="{
      'customer-manager-dialog': type === 'depositActivation',
      'customer-manager-dialog-en': lang === 'en'
    }"
    class="customer-manager">
    <div v-show="visible">
      <div v-if="type !== 'depositActivation'" class="close" @click="visible = false">
        <img src="@/assets/image/icons/icon-close-default.png" />
      </div>

      <div class="business-card">
        <img :src="customerManagerInfo.directorPhoto" class="customer-head" />
        <div class="customer-info">
          <div class="customer-title">
            <template v-if="lang === 'zh-CN'">
              <span>{{ customerManagerInfo.directorName }}</span>
              <div><p>高级客户经理</p></div>
            </template>
            <template v-else>
              <span>{{ customerManagerInfo.directorNameEn }}</span>
            </template>
          </div>
          <div class="business">
            <p v-if="lang === 'zh-CN'">
              请认准【企业信息】为：<br/>
              <span>@CB International Bank</span>
            </p>
            <p v-else>
              Senior Account Manager
            </p>
          </div>
        </div>
        <img :src="customerManagerInfo.qrCode" class="customer-code" />
      </div>
    </div>
    <div v-show="!visible" class="customer-manager-suspension" @click="visible = true">
      <img src="@/assets/image/home/<USER>" />
      <div>
        <span>{{ lang === 'zh-CN' ? '专属客户经理' : 'Dedicated Account Manager' }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  props: {
    customerManagerInfo: {
      type: [Object],
      default: function () {
        return {}
      }
    },
    type: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      visible: true
    }
  },
  computed: {
    ...mapState('app', ['lang'])
  },
  methods: {

  }
}
</script>

<style lang="less" scoped>

.customer-manager {
  position: fixed;
  right: 30px;
  bottom: 45px;
  z-index: 100;

  &.customer-manager-dialog-en {
    .business-card {
      .customer-info {
        .business {
          // width: 126px;
          height: 23px;
          background-color: #CDDCFB;
          box-shadow: 0px 2px 5px 0px rgba(107,149,223,0.15);
          border-radius: 2px;
        }
      }
    }

    .customer-manager-suspension {
      width: 170px;
    }
  }

  &.customer-manager-dialog {
    position: relative;
    right: 0;
    bottom: 0;
    .customer-manager-suspension {
      width: 100%;
      height: 128px;
    }

    .business-card {
      width: 100%;
      height: 100%;
      padding: 12px 14px;
      justify-content: flex-start;

      .customer-code {
        width: 85px;
        height: 85px;
        margin-left: auto;
      }

      .customer-head {
        width: 92px;
        height: 92px;
      }

      .customer-info {
        margin-left: 26px;
        .business {
          width: 185px;
          height: 50px;
          padding-left: 15px;

          span {
            margin-top: 0;
          }
        }
        .customer-title {
          display: flex;
          align-items: center;
          margin-bottom: 14px;
          > span {
            color: #333752;
            font-size: 12px;
            font-weight: 500;
          }
          div {
            width: 88px;
            height: 22px;
          }
        }
      }
    }
  }

  .customer-manager-suspension {
    width: 106px;
    height: 106px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;

    img {
      width: 64px;
      height: 64px;
      margin-top: 2px;
    }

    > div {
      width: 100%;
      height: 26px;
      background-color: #DBE2FF;
      border-radius: 2px;
      color: #333752;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;

      span {
        // transform: scale(0.8);
      }
    }

    &:hover {
      > div {
        background-color: #C8D4FF;
      }
    }
  }

  .close {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 6px;
    cursor: pointer;

    img {
      width: 24px;
      height: 24px;
      display: block;
    }
  }

  .business-card {
    width: 354px;
    height: 108px;
    padding: 12px 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: url('../../../assets/image/home/<USER>') no-repeat;
    background-size: 100% 100%;

    .customer-head {
      width: 84px;
      height: 84px;
      border-radius: 50%;
    }

    .customer-info {
      .customer-title {
        display: flex;
        align-items: center;
        // justify-content: space-between;
        margin-bottom: 14px;
        > span {
          color: #333752;
          font-size: 12px;
          font-weight: 500;
        }
        div {
          width: 80px;
          height: 18px;
          background-color: #CDDCFB;
          border-radius: 2px 2px 2px 2px;
          margin-left: 10px;

          p {
            text-align: center;
            line-height: 18px;
            color: #333752;
            font-size: 12px;
            font-weight: 400;
            display: block;
          }
        }
      }

      .business {
        width: 156px;
        height: 38px;
        padding-left: 5px;
        background: #FFFFFF;
        box-shadow: 0px 2px 5px 0px rgba(107,149,223,0.15);
        border-radius: 2px 2px 2px 2px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #333752;
        font-size: 12px;

        p {
          width: 100%;
          // transform: scale(0.8);
        }

        span {
          color: #FF911C;
          margin-top: 6px;
          display: block;
        }
      }
    }

    .customer-code {
      width: 66px;
      height: 66px;
    }
  }
}
</style>
