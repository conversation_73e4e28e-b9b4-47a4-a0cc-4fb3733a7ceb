<template>
  <div class="todo-list-container">
    <div class="header">
      <div class="title-info">
        <h3
          class="title">{{ $t('待办事项') }}（{{ $t('共') }} {{ todoListNum }}）
        </h3>
        <span
          class="btn"
          @click="handleViewMore">
          {{ $t('更多') }}
        </span>
      </div>
      <p class="desc">{{$t('请尽快妥善处理此类事项，以避免可能出现的资金被退回、冻结或账户被限制等情况')}}</p>
    </div>
    <div class="content">
      <DataTable
        :data="tableData"
        :empty-text="$t('暂无待办事项')"
        class="data-table"
        v-loading="tableLoading">
        <el-table-column
          :label="$t('交易类别')"
          prop="businessName">
          <template slot-scope="{row}">
            <div :class="{'emergency': row.tableDataType === 'TODOLIST'}">
              {{ businessName(row) }}
            </div>
            <!-- <div v-if="row.tableDataType === 'DIRECTIVE'">
              {{ $t('{businessName}有{totalNumber}笔待处理指令', {
                businessName: $t(row.businessName),
                totalNumber: row.totalNumber
              }) }}
            </div>
            <div v-if="row.tableDataType === 'TODOLIST'" class="emergency">
              {{ row.businessSubType === 'RFI_COMPLIANCE' ? $t('尾号为{accNoSimp}的账户有一笔跨行汇入{currency} {amount}的在途交易待处理', row) : $t('尾号为{accNoSimp}的账户有一笔跨行汇入{currency} {amount}的汇款凭证待处理', row) }}
            </div> -->
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('操作')"
          width="170">
          <template slot-scope="scope">
            <el-link
              type="primary"
              @click="handleSettle(scope.row)">
              {{ $t('立即处理') }}
            </el-link>
          </template>
        </el-table-column>
      </DataTable>
    </div>
  </div>
</template>

<script>
import DataTable from '@/components/table/DataTable'
import api from '../api'
import todoApi from '@/views/accountManage/business/todoList/api/list'
import { mapState, mapMutations } from 'vuex'
export default {
  name: 'TodoList',
  components: {
    DataTable
  },
  data () {
    return {
      tableData: [],
      tableLoading: false
    }
  },
  computed: {
    ...mapState('home', ['todoListNum'])
  },
  created () {
    this.getTableData()
  },
  methods: {
    ...mapMutations('home', ['SET_TODO_LIST_NUM']),
    // 代办事项 - 交易类别
    businessName (e) {
      const { tableDataType, businessName, totalNumber, businessSubType } = e

      if (tableDataType === 'DIRECTIVE') {
        return this.$t('{businessName}有{totalNumber}笔待处理指令', {
          businessName: this.$t(businessName),
          totalNumber
        })
      }

      if (tableDataType === 'TODOLIST') {
        if (businessSubType === 'RFI_COMPLIANCE') {
          return this.$t('尾号为{accNoSimp}的账户有一笔跨行汇入{currency} {amount}的在途交易待处理', e)
        } else if (businessSubType === 'RFI_OUTWARD_PEER_BANK') {
          return this.$t('尾号为{accNoSimp}的账户有一笔同行汇出{currency} {amount}的在途交易待处理', e)
        } else if (businessSubType === 'RFI_OUTWARD_INTER_BANK') {
          return this.$t('尾号为{accNoSimp}的账户有一笔跨行汇出{currency} {amount}的在途交易待处理', e)
        } else if (businessSubType === 'RFI_OUTWARD_SALARY_PAYMENT') {
          return this.$t('尾号为{accNoSimp}的账户有一笔薪酬发放{currency} {amount}的在途交易待处理', e)
        } else if (businessSubType === 'RFI_OUTWARD_SALARY_PEER_PAYROLL' || businessSubType === 'RFI_OUTWARD_SALARY_GLOBAL_PAYROLL') {
          return this.$t('尾号为{accNoSimp}的账户有一笔{businessSubType} {currency} {amount}的在途交易待处理', {
            ...e,
            businessSubType: this.lang === 'en' ? e.businessSubTypeStrEn : e.businessSubTypeStr
          })
        }
        return this.$t('尾号为{accNoSimp}的账户有一笔跨行汇入{currency} {amount}的汇款凭证待处理', e)
      }
    },
    getTableData () {
      this.tableLoading = true
      return Promise.all([this.getTodoList(), this.getTodoTableData()]).then(res => {
        // 手动添加上自定义标识 'DIRECTIVE' 'TODOLIST'
        const todoList = (res[0] || []).map(item => {
          return {
            ...item,
            tableDataType: 'DIRECTIVE'
          }
        })
        const { records, total } = res[1] || { total: 0 }
        const todoTableData = (records || []).map(item => {
          return {
            ...item,
            tableDataType: 'TODOLIST'
          }
        })
        // 首页展示的指令和待处理事项 总数不超过5条
        this.tableData = todoList.concat(todoTableData).filter((item, index) => index <= 4)
        // 总长度
        const totalNum = todoList.length + total
        this.SET_TODO_LIST_NUM(totalNum)
      }).finally(_ => {
        this.tableLoading = false
      })
    },
    // 指令
    getTodoList () {
      return api.getTodoList().then(res => {
        if (res) {
          // 过滤掉 下挂账号转账
          return res.filter(item => item.businessCode !== '10200001')
        }
      }).catch(err => {
        console.error(err)
      })
    },
    // 待处理事项
    getTodoTableData () {
      return todoApi.getTodoTableData({
        backlogStatus: 'WAIT',
        pageNo: 1,
        pageSize: 10
      }).then(res => {
        if (res) {
          return res
        }
      }).catch(err => {
        console.error(err)
      })
    },
    handleSettle (row) {
      const { tableDataType, businessCode } = row
      // 指令
      if (tableDataType === 'DIRECTIVE') {
        if (businessCode === '10101300') {
          this.$router.push({
            name: 'RemitProof'
          })
          return
        }
        // 资金互转
        if (businessCode + '' === '10201200') {
          this.$router.push({
            name: 'DirectiveDealOfficialCardTransferList',
            query: {
              businessCode
            }
          })
          return
        }
        // 公务卡充值
        if (businessCode + '' === '10201201') {
          this.$router.push({
            name: 'DirectiveDealDebitCardTransferList',
            query: {
              businessCode
            }
          })
          return
        }
        this.$router.push({
          name: 'DirectiveDealQueryOrderList',
          query: {
            businessCode
          }
        })
      }
      // 待办事项
      if (tableDataType === 'TODOLIST') {
        const { rfiNo } = row
        this.$router.push({
          name: 'RfiAmlSubmit',
          query: {
            rfiNo
          }
        })
        // const { businessSubType, businessNo, onlineFlowNo, receiveBankCountry } = row
        // if (businessSubType === 'RFI_COMPLIANCE') {
        //   // RFI补充
        //   this.$router.push({
        //     name: 'RFIUpdate',
        //     query: {
        //       rfiNo: businessNo
        //     }
        //   })
        // } else if (businessSubType === 'RFI_OUTWARD_PEER_BANK' || businessSubType === 'RFI_OUTWARD_SALARY_PAYMENT') {
        //   // 同行/薪酬
        //   this.$router.push({
        //     name: 'RecordsOutwardDetail',
        //     query: {
        //       orderFlowNo: onlineFlowNo,
        //       businessCode: '********'
        //     }
        //   })
        // } else if (businessSubType === 'RFI_OUTWARD_INTER_BANK') {
        //   // 跨行
        //   this.$router.push({
        //     name: 'RecordsOutwardDetail',
        //     query: {
        //       orderFlowNo: onlineFlowNo,
        //       businessCode: '********'
        //     }
        //   })
        // } else if (businessSubType === 'RFI_OUTWARD_SALARY_PEER_PAYROLL' || businessSubType === 'RFI_OUTWARD_SALARY_GLOBAL_PAYROLL') {
        // // 薪酬发放
        //   const name = businessSubType === 'RFI_OUTWARD_SALARY_PEER_PAYROLL'
        //     ? 'SalaryInnerBankSalaryHistoryTransactionDetails'
        //     : receiveBankCountry === 'PK' ? 'SalaryPakistanSalaryHistoryTransactionDetails' : 'SalaryIndiaSalaryHistoryTransactionDetails'
        //   // receiveBankCountry：'PK' 巴基斯坦 'IN ' 印度
        //   this.$router.push({
        //     name,
        //     query: {
        //       salaryTradeNo: onlineFlowNo
        //     }
        //   })
        // } else {
        //   // 汇款凭证补充
        //   this.$router.push({
        //     name: 'RemitProofUpdate',
        //     query: {
        //       recvFlowNo: businessNo
        //     }
        //   })
        // }
      }
    },
    handleViewMore () {
      this.$router.push({
        name: 'TodoList'
      })
    }
  }
}
</script>

<style lang="less" scoped>
  .todo-list-container {
    background-color: #FFFFFF;
    border-radius: 4px;
    overflow: hidden;
    .header {
      padding: 20px 15px;
      border-bottom: 1px solid @blueLess;
      .title-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .title {
          color: #333752;
          font-size: 16px;
          font-weight: 300;
        }
        .btn {
          color: @blue;
          font-size: 14px;
          cursor: pointer;
        }
      }
      .desc {
        margin-top: 10px;
        font-size: 14px;
        font-family: PingFangSC-Light, PingFang SC;
        font-weight: 300;
        color: #FF8D4F;
        line-height: 14px;
      }
    }
    .content {
      padding: 0 15px;
      .data-table {
        margin: 17px 0;
        .emergency {
          position: relative;
          &::before {
            position: absolute;
            top: 2px;
            left: -15px;
            color: #F34359;
            width: 6px;
            height: 6px;
            content: '*';
            // display: block;
          }
        }
      }
    }
  }
</style>
