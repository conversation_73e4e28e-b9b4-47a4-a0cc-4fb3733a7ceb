<template>
  <div class="record-detail-delare-info">
    <div class="title">
      {{ $t('补充信息') }}
    </div>

    <el-form
      ref="declareForm"
      :model="formData"
      :rules="rules"
      label-width="170px"
      label-position="left">
      <div class="payee-list-filter">
        <!-- 已关闭、已退款、待处理这三种状态展示 -->
        <el-form-item
          v-if="rfiDetail.rfiStatus === 'CLOSED' || rfiDetail.rfiStatus === 'RETURNED' || rfiDetail.rfiStatus === 'WAIT_SUBMIT'"
          :label="$t('发起时间') + '：'"
          prop="searchAccount">
          {{ rfiDetail.launchTime }}
        </el-form-item>
        <!-- 已关闭、已退款、待处理这三种状态展示 -->
        <el-form-item
          v-if="rfiDetail.rfiStatus === 'CLOSED' || rfiDetail.rfiStatus === 'RETURNED' || rfiDetail.rfiStatus === 'WAIT_SUBMIT'"
          prop="searchAccount">
          <label slot="label" class="colorRed">
            {{ $t('截止时间') }}：
          </label>
          <span class="colorRed">{{ rfiDetail.deadline }}</span>
        </el-form-item>
        <!-- 已接收、已汇出这两种状态展示 -->
        <el-form-item
          v-if="rfiDetail.rfiStatus === 'RECEIVE' || rfiDetail.rfiStatus === 'REMITTED'"
          :label="$t('提交时间') + '：'"
          prop="searchAccount">
          {{ rfiDetail.submitTime }}
        </el-form-item>
        <el-form-item v-if="rfiDetail.rfiNo" :label="$t('说明') + '：'" prop="searchAccount">
          <span class="colorRed">
            {{ rfiDetail.remark }}
          </span>
        </el-form-item>
        <!-- <el-form-item :label="$t('收款人类型') + '：'" prop="searchAccount">
          {{ lang === 'en' ? rfiDetail.recvCstTypeEn : rfiDetail.recvCstTypeCn }}
        </el-form-item> -->
        <!-- <el-form-item :label="$t('与收款人关系') + '：'" prop="recvCstRelation"> -->
          <!-- 待处理状态 && 同行/跨行才可修改 -->
          <!-- <el-select
            v-if="rfiDetail.rfiStatus === 'WAIT_SUBMIT' && (rfiDetail.tradeBusinessSubType === '00' || rfiDetail.tradeBusinessSubType === '10')"
            v-model="formData.recvCstRelation"
            :placeholder="$t('请选择')"
            class="form-item"
            style="width: 500px;"
          >
            <el-option
              v-for="(item, index) in relationTypeList"
              :key="index"
              :label="lang === 'en' ? item.showNameEn : item.showName"
              :value="item.name" />
          </el-select>
          <span v-else>
            {{ lang === 'en' ? rfiDetail.recvCstRelationEn : rfiDetail.recvCstRelationCn }}
          </span>
        </el-form-item> -->
        <!-- <el-form-item v-if="formData.recvCstRelation === '220'" label="" prop="recvCstRelationOther" >
          <el-input
            v-if="rfiDetail.rfiStatus === 'WAIT_SUBMIT' && (rfiDetail.tradeBusinessSubType === '00' || rfiDetail.tradeBusinessSubType === '10')"
            v-model="formData.recvCstRelationOther"
            :placeholder="$t('请输入字母、数字、空格或/.,?:()-+{}，不超过110个字符')"
            :maxlength="110"
            :rows="3"
            :show-word-limit="true"
            type="textarea"
            class="union-field"
          />
          <span v-else>
            {{ formData.recvCstRelationOther }}
          </span>
        </el-form-item> -->
        <!-- <div v-if="rfiDetail.tradeBusinessSubType !== 'SALARY_PAYMENT'">
          <el-form-item :label="$t('汇款用途/目的') + '：'" prop="recvCstPurpose">
            <el-cascader
              v-if="rfiDetail.rfiStatus === 'WAIT_SUBMIT' && (rfiDetail.tradeBusinessSubType === '00' || rfiDetail.tradeBusinessSubType === '10')"
              v-model="formData.recvCstPurpose"
              popper-class="recvCstPurpose-popper"
              :options="popuseTypeList"
              :show-all-levels="false"
              :placeholder="$t('请选择')"
              :props="{
                emitPath: false,
                value: 'name',
                label: lang === 'en' ? 'showNameEn' : 'showName',
                children: 'elements'
              }"
              style="width: 500px;"
              filterable
            />
            <span v-else>
              {{ lang === 'en' ? rfiDetail.recvCstPurposeEn : rfiDetail.recvCstPurposeCn }}
            </span>
          </el-form-item>
          <el-form-item
            v-if="payUseOtherFlag"
            label=""
            prop="recvCstPurposeOther">
            <el-input
              v-if="rfiDetail.rfiStatus === 'WAIT_SUBMIT' && (rfiDetail.tradeBusinessSubType === '00' || rfiDetail.tradeBusinessSubType === '10')"
              :maxlength="140"
              class="union-field"
              type="textarea"
              :rows="3"
              :show-word-limit="true"
              :placeholder="$t('请输入字母、数字、空格或/.,?:()-+{}，不超过140个字符')"
              v-model="formData.recvCstPurposeOther"
            />
            <span v-else>
              {{ rfiDetail.recvCstPurposeOther || formData.recvCstPurposeOther }}
            </span>
          </el-form-item>
        </div> -->
        <el-form-item :label="$t('资金证明材料') + '：'" prop="fileList">
          <!-- 只有待处理状态才可上传 -->
          <UploadFile
            :disabled="rfiDetail.rfiStatus !== 'WAIT_SUBMIT'"
            :file-list="formData.fileList"
            uploadType="oss"
            class="form-item">
          </UploadFile>
        </el-form-item>
        <el-form-item v-if="rfiDetail.rfiStatus === 'WAIT_SUBMIT'">
          <div style="display: flex; align-items: flex-start; width: 500px;">
            <span style="color:#F34359; line-height: 20px; margin-right: 9px;">*</span>
            <div>
              <p class="tip-item">
                {{ $t('请仔细阅读补充说明要求，并提供相关证明材料。') }}
              </p>
              <p class="tip-item">
                {{ $t('单个xls、pdf、jpg、jpeg、png等文件不超过10M，最多上传10个文件') }}
              </p>
            </div>
          </div>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
import { genRequiredRule, genRegTestRule } from '@/utils/form/genrules'
import { getPurpose } from '../api/index'
import { mapState } from 'vuex'
// 普通跨行 10 普通行内 00 薪酬发放 SALARY_PAYMENT
export default {
  props: {
    orderFlowNo: {
      type: String,
      default: ''
    },
    rfiDetail: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data () {
    return {
      formData: {
        recvCstRelation: '',
        recvCstRelationOther: '',
        recvCstPurpose: '',
        recvCstPurposeOther: '',
        fileList: []
      },

      relationTypeList: [],
      popuseTypeList: []
    }
  },
  computed: {
    ...mapState('app', ['lang']),
    // 汇款用途是否为其他
    payUseOtherFlag () {
      const { recvCstPurpose } = this.formData

      return this.$_params.payUseOther.includes(recvCstPurpose)
    },
    rules () {
      const { tradeBusinessSubType, rfiStatus } = this.rfiDetail
      return {
        recvCstRelation: [
          rfiStatus !== 'WAIT_SUBMIT' || tradeBusinessSubType === 'SALARY_PAYMENT' ? {} : { required: true, message: this.$t('请选择'), trigger: 'change' }
        ],
        recvCstRelationOther: [
          rfiStatus === 'WAIT_SUBMIT' ? genRequiredRule('请输入与收款人关系', 'blur') : {},
          genRegTestRule(/^[a-zA-Z\d /.,?:()\-+{}]{1,110}$/, '您输入的字符格式不正确，请输入字母、数字、空格或/.,?:()-+{}')
        ],
        recvCstPurpose: [
          rfiStatus !== 'WAIT_SUBMIT' || tradeBusinessSubType === 'SALARY_PAYMENT' ? {} : { required: true, message: this.$t('请选择'), trigger: 'change' }
        ],
        recvCstPurposeOther: [
          genRequiredRule('请输入汇款用途'),
          genRegTestRule(/^[a-zA-Z\d /.,?:()\-+{}]{1,140}$/, '您输入的字符格式不正确，请输入字母、数字、空格或/.,?:()-+{}')
        ],
        fileList: { required: rfiStatus === 'WAIT_SUBMIT', message: this.$t('请上传资金证明材料'), trigger: 'change' }
      }
    }
  },
  created () {
    this.initData()

    const { rfiStatus } = this.rfiDetail
    if (rfiStatus === 'WAIT_SUBMIT') {
      this.getPurposeList()
    }
  },
  methods: {
    initData () {
      Object.keys(this.formData).forEach((key) => {
        if (key === 'fileList') {
          if (this.rfiDetail.fileList && this.rfiDetail.fileList.length > 0) {
            this.rfiDetail.fileList.map(fileItem => {
              fileItem.name = fileItem.fileName
              fileItem.path = fileItem.fileNo
              fileItem.uploadType = fileItem.pathType === 'FTP' ? 'compliance' : (fileItem.pathType || 'oss')
            })
          }
          console.log(this.rfiDetail.fileList)
        }
        this.formData[key] = this.rfiDetail[key] || this.formData[key]
      })
    },
    validate () {
      return new Promise((resolve, reject) => {
        this.$refs.declareForm.validate(valide => {
          if (valide) {
            resolve({
              ...this.formData
            })
          } else {
            // eslint-disable-next-line prefer-promise-reject-errors
            reject(false)
          }
        })
      })
    },
    getPurposeList () {
      getPurpose({
        onlineFlowNo: this.orderFlowNo
      }).then(res => {
        if (res && res.length > 0) {
          res.map(item => {
            const { key, elements } = item

            if (key === 'recvRelationEnum') {
              this.relationTypeList = elements || []
            }
            if (key === 'purposeEnum') {
              this.popuseTypeList = elements || []
            }
          })
        }
      }).catch(err => console.error(err))
    }
  }
}
</script>

<style lang="less" scoped>
.record-detail-delare-info {
  .union-field {
    width: 500px;
  }
  .tip-item {
    font-size: 12px;
    font-weight: 300;
    color: #A1A7C4;
    line-height: 17px;
  }
  .colorRed {
    color: @red;
  }
  .title {
    color: #131523;
    font-size: 16px;
    font-weight: bold;
    margin: 40px 0 30px;
    position: relative;

    &::before {
      content: '';
      width: 2px;
      height: 14px;
      background: #4A6AFF;
      border-radius: 2px;
      position: absolute;
      left: -13px;
      top: 0;
    }
  }
}
</style>
