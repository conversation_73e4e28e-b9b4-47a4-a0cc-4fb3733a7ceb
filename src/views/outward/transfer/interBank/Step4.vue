
<template>
  <div>
    <TransferResultWithNoticy
      :transferStatus="transferStatus"
      :transferResult="transferResult"
      :title="title"
      :tipMessage="tipMessage"
      @goBack="goBack"
      @againCommit="againCommit"
      @continueTransfer="goBack"
    >
      <!-- 您可稍后到“汇出汇款-汇款记录查询”页面中获取银行处理结果 -->
      <div slot="customer" v-if="transferResult.orderState === '42' || transferResult.orderState === '98' || transferResult.orderState === '50' || transferResult.orderState === '51'">
        <div
          class="to-query-transfer-result">
          <i18n tag="p" path="toQueryTransferResult">
            <template v-slot:queryPage>
              <span @click="handleQueryTransferResult">
                {{ $t('汇出汇款-汇款记录查询') }}
              </span>
            </template>
          </i18n>
        </div>
        <SubmitRfiAml
          :ref-no="transferResult.orderFlowNo"
          style="margin: 15px auto 0;max-width: 800px;">
        </SubmitRfiAml>
      </div>
      <LeadToBindWechatPublic slot="customer" style="margin-top: 30px;" v-if="lang === 'zh-CN'"></LeadToBindWechatPublic>
    </TransferResultWithNoticy>
  </div>
</template>

<script>
import TransferResultWithNoticy from '../../components/TransferResultWithNoticy'
import { mapState } from 'vuex'
import SubmitRfiAml from '@/views/accountManage/business/todoList/rfi-aml/components/SubmitRfiAml'

export default {
  name: 'step4_cache',
  components: {
    TransferResultWithNoticy,
    SubmitRfiAml
  },
  data () {
    return {
      transferResult: {},
      transferStatus: 'fail',
      title: '跨行汇款结果',
      tipMessage: '操作完成，请等待审核',
      checkNames: []
    }
  },
  created () {
    const query = this.$route.query
    // 处理一下时间格式
    query.orderSubmitTime = this.$_format.dataToDate(query.orderSubmitTime)
    // 处理状态指令
    query.orderStateShow = this.$_params.getDisplay('ORDER_STATUS', query.orderState)
    this.transferResult = query
    this.initData()
  },
  computed: {
    ...mapState('app', ['lang'])
  },
  methods: {
    // 查询汇款结果
    handleQueryTransferResult () {
      const { orderFlowNo } = this.transferResult
      this.$router.push({
        name: 'RecordsOutwardDetail',
        query: {
          orderFlowNo,
          businessCode: '10200300'
        }
      })
    },
    // 初始化页面数据
    initData () {
      const { orderState, iUserList = [] } = this.transferResult
      this.checkNames = iUserList.map(item => item.userName)
      // 转账成功
      if (orderState === '11' || orderState === '42') {
        this.transferStatus = 'success'
        this.tipMessage = '您的汇款申请已提交成功'
      } else if (orderState === '98' || orderState === '50') { // 交易可疑/处理中 - 展示银行处理中
        this.transferStatus = 'pending'
        this.tipMessage = '银行处理中'
      } else if (orderState === '99') { // 转账失败
        this.transferStatus = 'fail'
        if (!this.$_params.isEmpty(this.transferResult.em)) {
          // this.tipMessage = this.transferResult.em
          this.tipMessage = '您的汇款申请已提交失败'
        } else {
          // this.tipMessage = '交易失败'
          this.tipMessage = '您的汇款申请已提交失败'
        }
      } else if (orderState === '02') {
        this.transferStatus = 'complete'
        this.tipMessage = this.$t('您的汇款申请已提交') + '，' + this.$t('需') + this.checkNames.join() + this.$t('复核')
      } else { // 操作完成，请等待审核
        this.transferStatus = 'complete'
        this.tipMessage = '操作完成，请等待审核'
      }
    },

    // 返回
    goBack () {
      // 跳转到第一步
      this.$router.push({
        name: 'InterBankOutwardStep1',
        params: {
          routeType: 'NO_CACHE'
        }
      })
    },
    // 重新提交
    againCommit () {
      // 跳转到第一步
      this.$router.push({
        name: 'InterBankOutwardStep1',
        params: {
          routeType: 'CACHE'
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.to-query-transfer-result {
  color: #333752;
  font-size: 14px;
  margin: 30px auto 0;
  text-align: center;

  span {
    cursor: pointer;
    color: #4A6AFF;
  }
}
</style>
