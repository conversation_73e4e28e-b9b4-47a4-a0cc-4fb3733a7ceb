<template>
  <el-dialog
    v-bind="$attrs"
    v-on="$listeners"
    width="70%"
    :close-on-click-modal="false"
    :modal-append-to-body="false"
    :show-close="true"
    :title="$t(title)"
  >
    <div class="w100">
      <div class="title-container">
        <p class="title">{{ $t("订单信息") }}</p>
      </div>
      <!-- 详情 -->
      <div v-if="componentsType === 'details'" class="details-info-container">
        <el-row>
          <el-col :span="12" v-for="(item, index) in infoClumList" :key="index">
            <div class="info-container">
              <div class="label">
                {{ $t(item.label) + "：" }}
              </div>
              <div class="value">
                <template
                  v-if="
                    item.prop === 'storeCode' && formData[item.prop] === '其他'
                  "
                >
                  {{ $t(formData["storeCodeOtherValue"]) }}
                </template>
                <template v-else>
                  {{
                    lang === "en" && item.propEn
                      ? $t(formData[item.propEn])
                      : $t(formData[item.prop])
                  }}
                </template>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-container">
              <div class="label">{{ $t("物流单据") }}：</div>
              <div class="value">
                <ViewFile
                  :file-list="formData.logisticsFiles"
                  upload-type="settle"
                >
                </ViewFile>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-container">
              <div class="label">{{ $t("其他文件") }}：</div>
              <div class="value">
                <ViewFile :file-list="formData.otherFiles" upload-type="settle">
                </ViewFile>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <!-- 添加 -->
      <el-form
        v-else
        :model="formData"
        class="w100"
        ref="form"
        label-width="150px"
        label-position="top"
        :rules="rules"
      >
        <el-row type="flex" justify="center">
          <el-col :span="12" class="mr40">
            <el-form-item :label="$t('销售订单号')" prop="purchaseOrderNo">
              <el-input
                class="w500"
                v-model="formData.purchaseOrderNo"
                :placeholder="$t('请输入对应平台/店铺销售订单号')"
                maxlength="32"
                show-word-limit
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('销售订单日期')" prop="orderDate">
              <el-date-picker
                class="w100"
                v-model="formData.orderDate"
                :placeholder="$t('请选择订单日期')"
                style="flex: 1 1 0%"
                value-format="yyyy-MM-dd"
                :picker-options="pickerOptions"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center">
          <el-col :span="12" class="mr40">
            <el-form-item :label="$t('订单金额')" prop="payLocal">
              <div class="input-container">
                <el-input
                  v-model.trim="formData.payLocal"
                  :placeholder="$t('请输入订单金额')"
                >
                </el-input>
                <div class="append-right">{{ settleData.currencyId }}</div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('商品类型')" prop="productId">
              <el-select
                v-model="formData.productId"
                class="w100"
                :placeholder="$t('请选择商品类型')"
              >
                <el-option
                  v-for="item in settleSelectMap.goodType"
                  :key="item.code"
                  :label="lang === 'en' ? item.showNameEn : item.showName"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center">
          <el-col :span="12" class="mr40">
            <el-form-item :label="$t('店铺/网站链接')" prop="storeLink">
              <el-input
                v-model="formData.storeLink"
                :placeholder="$t('请输入店铺/网站链接')"
                maxlength="90"
                show-word-limit
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('电商平台名称')" prop="storeCode">
              <div v-if="componentsType === 'details'">
                {{ formData.storeCode }}
              </div>
              <el-select
                v-else
                v-model="formData.storeCode"
                class="w100"
                filterable
                :placeholder="$t('请选择')"
              >
                <el-option
                  v-for="item in platformNameTypeList"
                  :key="item.name"
                  :label="item.showName"
                  :value="item.name"
                >
                </el-option>
              </el-select>
              <el-form-item
                v-if="formData.storeCode === '其他'"
                prop="storeCodeOtherValue"
              >
                <el-input
                  v-model="formData.storeCodeOtherValue"
                  :placeholder="$t('请输入')"
                  maxlength="32"
                  show-word-limit
                  style="margin-top: 10px"
                >
                </el-input>
              </el-form-item>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center">
          <el-col :span="12" class="mr40">
            <el-form-item :label="$t('平台店铺ID')" prop="storeId">
              <el-input
                v-model="formData.storeId"
                :placeholder="$t('请输入电商平台店铺ID')"
                maxlength="64"
                show-word-limit
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('买家类型')" prop="buyerType">
              <div v-if="componentsType === 'details'">
                {{
                  lang === "en" ? formData.buyerTypeEn : formData.buyerTypeCn
                }}
              </div>
              <el-select
                v-else
                v-model="formData.buyerType"
                class="w100"
                filterable
                :placeholder="$t('请选择')"
              >
                <el-option
                  v-for="item in settleSelectMap.buyerType"
                  :key="item.name"
                  :label="lang === 'en' ? item.showNameEn : item.showName"
                  :value="item.name"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center">
          <el-col :span="12" class="mr40">
            <el-form-item :label="$t('买家国家')" prop="buyerArea">
              <div v-if="componentsType === 'details'">
                {{
                  lang === "en" ? formData.buyerAreaEn : formData.buyerAreaCn
                }}
              </div>

              <el-select
                v-model="formData.buyerArea"
                :placeholder="$t('请选择')"
                style="width: 100%"
                filterable
              >
                <el-option
                  v-for="(item, index) in settleSelectMap.countryList"
                  :key="index"
                  :label="lang === 'en' ? item.enShortName : item.zhShortName"
                  :value="item.countryCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('买家名称')" prop="consigneeName">
              <div v-if="componentsType === 'details'">
                {{ formData.consigneeName }}
              </div>
              <el-input
                v-else
                v-model="formData.consigneeName"
                :placeholder="$t('请输入订单买家名称')"
                maxlength="32"
                show-word-limit
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center">
          <el-col :span="12" class="mr40">
            <el-form-item :label="$t('收货人地址')" prop="consigneeAddr">
              <el-input
                v-model="formData.consigneeAddr"
                :placeholder="$t('请输入订单收货人地址')"
                maxlength="128"
                show-word-limit
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('收货人电话')" prop="consigneeTel">
              <div v-if="componentsType === 'details'">
                {{ formData.consigneeTel }}
              </div>
              <el-input
                v-else
                v-model="formData.consigneeTel"
                :placeholder="$t('请输入订单收货人电话')"
                maxlength="32"
                show-word-limit
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center">
          <el-col :span="12" class="mr40">
            <el-form-item :label="$t('物流公司')" prop="logCompany">
              <el-input
                v-model="formData.logCompany"
                :placeholder="$t('请输入物流公司')"
                maxlength="64"
                show-word-limit
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('物流单号')" prop="orderSeqno">
              <el-input
                v-model="formData.orderSeqno"
                :placeholder="$t('请输入物流单号')"
                maxlength="32"
                show-word-limit
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('物流单据')" prop="logisticsFiles">
              <UploadFile
                upload-type="settle"
                :upload-data="{
                  businessFileType: 'SALES_ORDER_LOGISTICS_DOCUMENTS',
                }"
                :max-files="10"
                :file-list="formData.logisticsFiles"
              />
            </el-form-item>
            <el-form-item>
              <div class="tip-text">
                <p class="require">
                  {{
                    $t(
                      "1、已发货提供本次贸易的全链物流单据（包含国内段物流信息和国际段物流信息）。"
                    )
                  }}
                </p>
                <p>
                  {{
                    $t(
                      "2、未发货订单，可提现完成后补充物流单据，上笔提现资料未补充完整，本笔提现将受限。"
                    )
                  }}
                </p>
                <p>
                  {{
                    $t(
                      "3、支持pdf、xls、jpg、jpeg、png等格式，文件不超过10MB，最多可上传10个文件。"
                    )
                  }}
                </p>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('其他文件')" prop="otherFiles">
              <UploadFile
                upload-type="settle"
                :upload-data="{ businessFileType: 'SALES_ORDER_OTHER' }"
                :file-list="formData.otherFiles"
              />
            </el-form-item>
            <el-form-item>
              <div class="tip-text">
                <p class="require">
                  {{
                    $t(
                      "1、请上传能证明该笔提现的文件，包括但不限于后台交易明细文件、订单截图、物流单据截图等。"
                    )
                  }}
                </p>
                <p>
                  {{
                    $t(
                      "2、支持pdf、xls、jpg、jpeg、png等格式，文件不超过10MB，最多可上传10个文件。"
                    )
                  }}
                </p>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <!-- 商品信息 -->
      <div class="title-container">
        <p class="title">
          {{ $t("商品信息") }}
          <span v-if="componentsType !== 'details'">
            （{{ $t("必填项") }}）
          </span>
        </p>
        <el-button
          v-if="componentsType !== 'details'"
          class="right-button"
          type="primary"
          @click="handleAddGoods"
        >
          {{ $t("新增商品") }}
        </el-button>
      </div>
      <el-form style="width: 100%" :model="formData" ref="tableForm">
        <el-table :data="formData.goodList" style="width: 100%" border>
          <el-table-column
            prop="name"
            class-name="table-column"
            :label="$t('商品名称')"
            align="center"
          >
            <template slot-scope="scope">
              <el-form-item
                :prop="`goodList[${scope.$index}].name`"
                :rules="tableRules.name"
              >
                <template v-if="componentsType !== 'details'">
                  <el-input
                    v-model="scope.row.name"
                    :placeholder="$t('请输入商品名称')"
                    maxlength="64"
                    show-word-limit
                  >
                  </el-input>
                </template>
                <template v-else>
                  {{ scope.row.name }}
                </template>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="cnt" :label="$t('数量')" align="center">
            <template slot-scope="scope">
              <el-form-item
                :prop="`goodList[${scope.$index}].cnt`"
                :rules="tableRules.cnt"
              >
                <template v-if="componentsType !== 'details'">
                  <el-input
                    v-model.number="scope.row.cnt"
                    :placeholder="$t('请输入商品数量')"
                    maxlength="32"
                    show-word-limit
                  >
                  </el-input>
                </template>
                <template v-else>
                  {{ scope.row.cnt }}
                </template>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="price" :label="$t('单价')" align="center">
            <template slot-scope="scope">
              <el-form-item
                :prop="`goodList[${scope.$index}].price`"
                :rules="tableRules.price"
              >
                <template v-if="componentsType !== 'details'">
                  <el-input
                    v-model.trim="scope.row.price"
                    :placeholder="$t('请输入商品单价')"
                    maxlength="8"
                    show-word-limit
                  >
                  </el-input>
                </template>
                <template v-else>
                  {{ scope.row.price }}
                </template>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column
            v-if="componentsType !== 'details'"
            :label="$t('操作')"
            align="center"
          >
            <template slot-scope="scope">
              <el-button
                @click="handleRemove(scope.row, scope.$index)"
                type="text"
              >
                {{ $t("删除") }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </div>
    <!-- 底部按钮 -->
    <div class="button-group">
      <template v-if="componentsType === 'details'">
        <el-button type="info" @click="handleCancel">
          {{ $t("关闭") }}
        </el-button>
      </template>
      <template v-else>
        <el-button @click="handleCancel">
          {{ $t("取消") }}
        </el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="handelConfirm"
        >
          {{ $t("保存") }}
        </el-button>
      </template>
    </div>
  </el-dialog>
</template>

<script>
import settleApi from '../../../api/settle'
import { platformNameTypeList } from '../enum'

import mixins from '../settleMixin'
import { mapGetters, mapState } from 'vuex'
export default {
  components: {},
  props: {
    // add: 添加，details: 查看
    componentsType: {
      type: String,
      default: 'add'
    },
    title: {
      type: String,
      default: '新增订单'
    },
    infoData: {
      type: Object,
      default () {
        return {}
      }
    }
  },
  mixins: [mixins],
  data () {
    const priceValidator = (rule, value, callback) => {
      if (
        value !== '' &&
        (value === 0 || (value.indexOf(0) === 0 && value.indexOf('.') === -1))
      ) {
        return callback(new Error(this.$t('请检查格式，不能以0开始')))
      }
      return callback()
    }
    return {
      infoClumList: [
        { label: '订单号', prop: 'purchaseOrderNo' },
        { label: '订单日期', prop: 'orderDate' },
        { label: '订单金额', prop: 'payLocal' },
        { label: '商品类型', prop: 'productName' },
        { label: '店铺/网站链接', prop: 'storeLink' },
        { label: '电商平台名称', prop: 'storeCode' },
        { label: '平台店铺ID', prop: 'storeId' },
        { label: '买家类型', prop: 'buyerTypeCn', propEn: 'buyerTypeEn' },
        { label: '买家国家', prop: 'buyerAreaCn', propEn: 'buyerAreaEn' },
        { label: '买家名称', prop: 'consigneeName' },
        { label: '收货人地址', prop: 'consigneeAddr' },
        { label: '收货人电话', prop: 'consigneeTel' },
        { label: '物流公司', prop: 'logCompany' },
        { label: '物流单号', prop: 'orderSeqno' }
      ],
      pickerOptions: {
        disabledDate (time) {
          return (
            time.getTime() > Date.now() ||
            time.getTime() < Date.now() - 3600 * 1000 * 24 * 365
          )
        }
      },
      mimes: [
        {
          mime: 'application/pdf',
          regExp: /\.pdf$/,
          openInBrowser: true
        },
        {
          mime: 'image/jpg',
          regExp: /\.jpg$/,
          openInBrowser: true
        },
        {
          mime: 'image/jpeg',
          regExp: /\.jpeg$/,
          openInBrowser: true
        },
        {
          mime: 'image/png',
          regExp: /\.png$/,
          openInBrowser: true
        },
        {
          mime: 'application/vnd.ms-excel',
          regExp: /\.xls$/
        },
        {
          mime: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          regExp: /\.xlsx$/
        }
      ],
      rules: {
        purchaseOrderNo: [
          {
            required: true,
            message: this.$t('请输入销售订单号码'),
            trigger: 'blur'
          },
          {
            pattern: /^[\da-zA-Z_-]+$/,
            message: this.$t('订单号格式有误，仅支持字母、数字、-、_'),
            trigger: 'blur'
          }
        ],
        orderDate: [
          {
            required: true,
            message: this.$t('请选择销售订单日期'),
            trigger: 'blur'
          }
        ],
        payLocal: [
          {
            required: true,
            message: this.$t('请输入订单金额'),
            trigger: 'change'
          },
          {
            pattern: /(^[0-9]{1,}$)|(^[0-9]{1,}[.]{1}[0-9]{1,2}$)/,
            message: '请输入最多二位小数的数字',
            trigger: 'blur'
          },
          {
            validator: (rule, value, callback) => {
              if (value > 40000) {
                callback(new Error(this.$t('订单金额不能高于4万美元')))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        productId: [
          {
            required: true,
            message: this.$t('请选择商品类型'),
            trigger: 'change'
          }
        ],
        storeLink: [
          {
            required: true,
            message: this.$t('请输入商品/店铺'),
            trigger: 'blur'
          }
        ],
        storeCode: [
          {
            required: true,
            message: this.$t('请选择电商平台名称'),
            trigger: 'change'
          }
        ],

        storeCodeOtherValue: [
          { required: true, message: this.$t('请输入'), trigger: 'change' }
        ],
        buyerType: [
          { required: true, message: this.$t('请选择'), trigger: 'change' }
        ],
        buyerArea: [
          { required: true, message: this.$t('请选择'), trigger: 'change' }
        ],
        storeId: [
          {
            required: true,
            message: this.$t('请输入电商平台店铺ID'),
            trigger: 'change'
          }
        ],
        consigneeName: [
          {
            required: true,
            message: this.$t('请输入买家名称'),
            trigger: 'blur'
          }
        ],
        consigneeAddr: [
          {
            // required: true,
            message: this.$t('请输入订单收货人地址'),
            trigger: 'blur'
          },
          {
            validator: (rule, value, callback) => {
              const reg = /^[^\u3400-\u4DBF\u4E00-\u9FFF\uF900-\uFAFF]*$/u
              if (!reg.test(value)) {
                callback(new Error(this.$t('收货人地址仅支持英文')))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        consigneeTel: [
          { message: this.$t('请输入收货人电话'), trigger: 'blur' },
          {
            pattern: /^[\d]{1,}$/,
            message: this.$t('只支持数字'),
            trigger: 'blur'
          }
        ],
        logCompany: [
          {
            required: true,
            message: this.$t('请输入物流公司'),
            trigger: 'blur'
          }
        ],
        orderSeqno: [
          {
            required: true,
            message: this.$t('请输入物流单号'),
            trigger: 'blur'
          },
          {
            pattern: /^[\da-zA-Z]+$/,
            message: this.$t('只支持数字与字母'),
            trigger: 'blur'
          }
        ]
      },
      tableRules: {
        name: [
          {
            required: true,
            message: this.$t('请输入商品名称'),
            trigger: 'blur'
          }
        ],
        cnt: [
          {
            required: true,
            message: this.$t('请输入商品数量'),
            trigger: 'blur'
          },
          {
            pattern: /[\d]/,
            message: this.$t('请输入正整数'),
            trigger: 'blur'
          }
        ],
        price: [
          {
            required: true,
            message: this.$t('请输入商品单价'),
            trigger: 'blur'
          },
          { validator: priceValidator, trigger: 'blur' },
          {
            pattern: /(^[0-9]{1,}$)|(^[0-9]{1,}[.]{1}[0-9]{1,2}$)/,
            message: this.$t('请输入最多二位小数的数字'),
            trigger: 'blur'
          }
        ]
      },
      formData: {
        purchaseOrderNo: '',
        orderDate: '',
        productId: '',
        storeLink: '',
        storeCode: '',
        storeCodeOtherValue: '',
        storeId: '',
        consigneeName: '',
        consigneeAddr: '',
        consigneeTel: '',
        logCompany: '',
        orderSeqno: '',
        logisticsFiles: [],
        otherFiles: [],
        goodList: []
      },

      platformNameTypeList,
      submitLoading: false
    }
  },
  computed: {
    ...mapGetters({ lang: 'lang' }),
    ...mapState('login', ['loginInfo']),
    ...mapState('base', ['settleSelectMap']),
    productShowName () {
      if (this.formData.productId) {
        let item = {}
        item = this.goodsTypeList.find(
          (item) => item.code === this.formData.productId
        )
        return item.showName
      } else {
        return ''
      }
    }
  },
  watch: {
    infoData: {
      handler (newV) {
        if (this.$props.componentsType === 'add') {
          this.formData = {
            purchaseOrderNo: '',
            orderDate: '',
            productId: '',
            storeLink: '',
            storeCode: '',
            storeId: '',
            consigneeName: '',
            consigneeAddr: '',
            consigneeTel: '',
            logCompany: '',
            orderSeqno: '',
            logisticsFiles: [],
            otherFiles: [],
            goodList: []
          }
          this.$refs.form && this.$refs.form.resetFields()
        } else if (this.$props.componentsType === 'modify') {
          this.formData = JSON.parse(
            JSON.stringify({
              ...this.formData,
              ...newV,
              logisticsFiles: newV.logisticsFiles
                ? newV.logisticsFiles.map((item) => {
                  const { fileName: name, fileSerialNo: path } = item
                  return {
                    name,
                    path
                  }
                })
                : [],
              otherFiles: newV.otherFiles
                ? newV.otherFiles.map((item) => {
                  const { fileName: name, fileSerialNo: path } = item
                  return {
                    name,
                    path
                  }
                })
                : []
            })
          )
        } else {
          this.formData = JSON.parse(
            JSON.stringify({
              ...this.formData,
              ...newV,
              logisticsFiles: newV.logisticsFiles
                ? newV.logisticsFiles.map((item) => {
                  const { fileName: name, fileSerialNo: path } = item
                  return {
                    name,
                    path
                  }
                })
                : [],
              otherFiles: newV.otherFiles
                ? newV.otherFiles.map((item) => {
                  const { fileName: name, fileSerialNo: path } = item
                  return {
                    name,
                    path
                  }
                })
                : []
            })
          )
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted () {},
  methods: {
    handleAddGoods () {
      const data = {
        name: '',
        cnt: '',
        price: ''
      }
      this.formData.goodList.push(data)
    },
    handleRemove (item, index) {
      this.$tconfirm('是否确认删除该商品?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          this.formData.goodList.splice(index, 1)
        })
        .catch((_) => {
          console.log(_)
        })
    },
    handleCancel (e) {
      this.$emit('update:visible', false)
    },
    handelConfirm () {
      this.$refs.form.validate((valid, model) => {
        if (valid) {
          if (!this.formData.goodList.length) {
            this.$err('请添加商品信息')
            return
          }
          this.$refs.tableForm.validate((valid, model) => {
            if (!valid) return
            // 2022-05-09 ‘浙商+易宝’需求 去掉订单金额与商品总金额的校验
            // let num = 0
            // this.formData.goodList.forEach(item => {
            //   num += item.cnt * (item.price * 100) / 100
            // })
            // if (this.formData.payLocal * 100 !== num * 100) {
            //   this.$err('订单金额与商品总金额不一致')
            //   return
            // }
            const data = {
              ...this.formData,
              otherFiles: this.formData.otherFiles.map((item) => item.path),
              logisticsFiles: this.formData.logisticsFiles.map(
                (item) => item.path
              )
            }
            this.submitLoading = true
            const fn = this.$props.componentsType === 'add'
              ? settleApi.ccsOrderAdd
              : settleApi.ccsOrderUpdate
            fn({
              ...data,
              enterpriseCustomerNo: this.loginInfo.customerId
            })
              .then((res) => {
                this.$emit('confirm', data)
                this.handleCancel()
              })
              .catch(console.error)
              .finally((_) => {
                this.submitLoading = false
              })
          })
        }
      })
    },
    // 上传交易资料
    handleUploadlogisticsFiles (type, fileList) {
      this.formData[type] = fileList
    }
  }
}
</script>

<style lang="less" scoped>
.w100 {
  width: 100%;
}
.mr40 {
  margin-right: 40px;
}
.title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 22px;
  .title {
    position: relative;
    font-size: 16px;
    font-weight: 500;
    color: @blackLarge;
    line-height: 16px;
    &::before {
      content: "";
      position: absolute;
      border-left: 2px solid @blue;
      left: -15px;
      top: 50%;
      height: 100%;
      transform: translateY(-50%);
    }
    span {
      font-size: 14px;
      font-weight: 300;
      color: @blackLess;
      line-height: 14px;
    }
  }
  .right-button {
    min-width: 80px;
    height: 32px;
    font-size: 14px;
  }
}

.details-info-container {
  background: #f6f7fc;
  border-radius: 10px;
  padding: 40px;
  margin-bottom: 40px;

  .info-container {
    display: flex;
    font-size: 14px;
    font-weight: 300;
    color: @blackMore;
    line-height: 14px;
    .label {
      min-width: 150px;
      margin-bottom: 20px;
    }
    .btn {
      margin-left: 40px;
    }
  }
}

.tip-text {
  margin-left: 15px;
  font-size: 12px;
  font-weight: 300;
  color: @blackLess;
  line-height: 18px;
  & > .require {
    position: relative;
    &::before {
      content: "*";
      position: absolute;
      color: @red;
      left: -10px;
      top: 2px;
    }
  }
}
.input-container {
  display: flex;
  .append-right {
    margin-left: 15px;
    word-break: keep-all;
  }
}

/deep/ .el-table th.el-table__cell {
  background-color: @white;
}

/deep/ .el-table--border {
  border-right: 1px solid @grayLess10;
  border-bottom: 1px solid @grayLess10;
}
.button-group {
  text-align: right;
  margin-top: 31px;
}
</style>
