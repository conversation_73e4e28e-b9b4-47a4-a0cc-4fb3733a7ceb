<template>
  <ModuleSection title="提现记录">
    <el-form
      ref="queryForm"
      :model="formData"
      label-position="left"
      class="download-bill-query">
      <el-row class="row">
        <!-- <el-col :span="12">
          <el-form-item
            :label="$t('销售订单号码')"
            prop="purchaseOrder"
          >
            <el-input
              v-model="formData.purchaseOrder"
              :placeholder="$t('请输入销售订单号码')"
            />
          </el-form-item>
        </el-col> -->
        <el-col :span="12">
          <el-form-item
            :label="$t('提现类型') + '：'"
            prop="withdrawalType">
            <el-select
              v-model="formData.withdrawalType"
              filterable
              clearable
              style="width: 100%;"
              :placeholder="$t('请选择')">
              <el-option
                v-for="item in settleTypeList"
                :key="item.value"
                :label="$t(item.label)"
                :value="item.value"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="$t('提现申请金额')"
            prop="minAmount">
            <div class="split-container">
              <el-input-number
                class="start"
                v-model="formData.minAmount"
                controls-position="right"
                :placeholder="$t('请输入开始金额')"
                :min="0">
              </el-input-number>
              <el-form-item
                prop="maxAmount"
                class="end">
                <el-input-number
                  class="w100"
                  v-model="formData.maxAmount"
                  controls-position="right"
                  :placeholder="$t('请输入结束金额')"
                  :min="0">
                </el-input-number>
              </el-form-item>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="row">
        <!-- <el-col :span="12">
          <el-form-item
            :label="$t('商品名称')"
            prop="itemEm"
          >
            <el-input
              v-model="formData.itemEm"
              :placeholder="$t('请输入商品名称')"
            />
          </el-form-item>
        </el-col> -->
      </el-row>
      <el-row class="row">
        <el-col :span="12">
          <el-form-item
            :label="$t('收款人名称') + '：'"
            prop="customerName">
            <el-input
              v-model="formData.customerName"
              :placeholder="$t('请输入收款人名称')"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="$t('到账人民币金额')"
            prop="minRespRmbAmount">
            <div class="split-container">
              <el-input-number
                class="start"
                v-model="formData.minRespRmbAmount"
                controls-position="right"
                :placeholder="$t('请输入开始金额')"
                :min="0">
              </el-input-number>
              <el-form-item
                prop="maxRespRmbAmount"
                class="end">
                <el-input-number
                  class="w100"
                  v-model="formData.maxRespRmbAmount"
                  controls-position="right"
                  :placeholder="$t('请输入结束金额')"
                  :min="0">
                </el-input-number>
              </el-form-item>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="row">
        <el-col :span="12">
          <el-form-item
            :label="$t('提现发起时间')"
            prop="beginApplyTime">
            <div class="split-container">
              <el-date-picker
                class="start"
                v-model="formData.beginApplyTime"
                :placeholder="$t('请选择')"
                value-format="yyyy-MM-dd"/>
              <el-form-item
                prop="endApplyTime"
                class="end">
                <el-date-picker
                  class="w100"
                  v-model="formData.endApplyTime"
                  :placeholder="$t('请选择')"
                  value-format="yyyy-MM-dd"/>
              </el-form-item>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="$t('提现状态')"
            prop="status">
            <el-select
              v-model="formData.status"
              :placeholder="$t('请选择')"
              style="width:100%">
              <el-option
                :label="$t('全部')"
                value="">
              </el-option>
              <el-option
                v-for="(item, key) in settleSelectMap.settlementPlan"
                :key="key"
                :label="$i18n.local === 'en' ? item.showNameEn : item.showName"
                :value="item.name"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-button
            type="primary"
            @click="handleQuery">
            {{ $t('查询') }}
          </el-button>
          <el-button
            type="info"
            @click="handleReset">
            {{ $t('重置') }}
          </el-button>
        </el-col>
      </el-row>
    </el-form>
    <DataTable
      class="table"
      v-tloading="tableLoading"
      :columnList="columnList"
      :data="list">
      <el-table-column
        :label="$t('操作')"
        :width="130"
        prop="operate"
        fixed="right">
        <template slot-scope="{row}">
          <div>
            <el-link
              type="primary"
              @click="handleDetails(row)">
              {{$t('查看详情')}}
            </el-link>
          </div>
          <!-- 易宝，只有2.资料待补充能重新提交，浙商+易宝 2.资料待补充 11.备案失败能重新提交 -->
          <div v-if="row.channelCode === 'CBI_YEEPAY' && row.status == 2 || row.channelCode != 'CBI_YEEPAY' && (row.status == 2 || row.status == 11)">
            <el-link
              type="primary"
              @click="handleReSubmit(row)">
              {{$t('重新提交')}}
            </el-link>
          </div>
          <!-- 申请撤销：CBTJ-HF渠道的记录，状态为审核不通过、备案失败时显示撤销功能。浙商+易宝渠道的记录，状态是审核不通过时显示撤销功能。 -->
          <!-- CBI_YEEPAY: 易宝；CBI_CZBANK_YEEPAY: 浙商+易宝；CBTJ-HF：汇付 -->
          <div v-if="
            (row.settlementChannelCode == 'CBI_YEEPAY' && row.status == 2) ||
            (row.settlementChannelCode == 'CBI_CZBANK_YEEPAY' && row.status == 2) ||
            (row.settlementChannelCode != 'CBI_YEEPAY' && row.settlementChannelCode != 'CBI_CZBANK_YEEPAY' && (row.status == 2 || row.status == 11))
          ">
            <el-link
              type="primary"
              @click="handleOrderCancel(row)">
              {{$t('申请撤销')}}
            </el-link>
          </div>
          <div v-if="row.status == 1 || row.status == 3 || row.status == 4">
            <el-link
              type="primary"
              @click="handleAddFile(row)">
              {{$t('补充文件')}}
            </el-link>
          </div>
          <div v-if="row.status == 9">
            <el-link
              type="primary"
              @click="handleWithdrow(row)">
              {{$t('申请提现')}}
            </el-link>
          </div>
        </template>
      </el-table-column>
    </DataTable>
    <Pagination
      :total="total"
      @current-change="handleCurrentChange"/>
    <el-dialog
      :title="$t('补充文件')"
      :visible.sync="fileUplodVisible"
      @close="fileUplodVisible = false"
      width="720px">
      <el-form
        ref="submitForm"
        :model="otherFilesData"
        label-position="left"
        :rules="rules"
        label-width="180px">
        <el-form-item
          :label="$t('补充文件')"
          prop="otherFiles">
          <UploadFile
            upload-type="settle"
            :upload-data="{ businessFileType: 'TRADE_SUPPLEMENTARY_DOCUMENT' }"
            :file-list="otherFilesData.otherFiles" />
        </el-form-item>
      </el-form>
      <div class="tip-text">
        <p class="require">
          {{ $t('支持pdf、xls、jpg、jpeg、png等格式，文件不超过10MB，最多可上传10个文件。') }}
        </p>
      </div>
      <div class="btns">
        <el-button
          type="primary"
          :loading="confirmLoading"
          @click="handleConfirm">
          {{ $t('确定') }}
        </el-button>
        <el-button @click="fileUplodVisible = false">
          {{ $t('取消') }}
        </el-button>
      </div>
    </el-dialog>
  </ModuleSection>
</template>

<script>
import settleApi from '../api/settle'
import UploadFile from '@/components/upload/UploadFile'
import { mapState } from 'vuex'

export default {
  components: { UploadFile },
  data () {
    return {
      settleTypeList: [
        { label: '企业自提', value: 'ENTERPRISE_WITHDRAW_DEPOSIT' },
        { label: '供应商付款', value: 'SUPPLER_PAYMENT' }
      ],
      fileStatus: {
        1: '资料齐全',
        2: '资料待补充'
      },
      /**
       * status 状态枚举如下
       * IN_AUDIT(1, "已提交", "审核中"),
       * AUDIT_NOT_PASS(2, "资料待补充", "资料待补充"),
       * SUCCESS(3, "提现成功", "提现成功"),
       * FAILURE(4, "提现失败", "提现失败"),
       * CANCEL_PROCESSING(5, "撤销中", "撤销中"),
       * CANCEL_SUCCESS(6, "撤销成功", "撤销成功"),
       * RECORD_PROCESSING(7, "已提交", "备案中"),
       * REMITTANCE_PROCESSING(8, "汇出中", "汇出中"),
       * SETTLEMENT_REMITTANCE_FAILURE(9, "汇出失败", "汇出失败"),
       * PROCESSING(10, "处理中", "处理中"),
       * RECORD_FAILURE(11, "备案失败", "备案失败");
       * */

      formData: {
        withdrawalType: '',
        // purchaseOrder: '',
        // itemEm: '',
        minAmount: undefined,
        maxAmount: undefined,
        customerName: '',
        minRespRmbAmount: undefined,
        maxRespRmbAmount: undefined,
        beginApplyTime: '',
        endApplyTime: '',
        status: '',
        page: '1',
        pageSize: '10'
      },
      columnList: [
        { label: '提现时间', prop: 'applyTime', width: 190 },
        { label: '提现账号', prop: 'enterpriseCustomerAccount', width: 180 },
        { label: '提现金额', prop: 'amount', width: 140 },
        { label: '手续费', prop: 'transferCharge', width: 140 },
        { label: '币种', prop: 'currencyId', width: 120 },
        { label: '提现类型', prop: 'withdrawalTypeNameCn', width: 180 },
        { label: '收款人名称', prop: 'customerName', width: 180 },
        { label: '收款银行名称', prop: 'recvBankName', width: 180 },
        { label: '收款银行卡号', prop: 'bindCard', width: 220 },
        { label: '状态', prop: 'statusDesc', width: 180 },
        { label: '备注', prop: 'comment', width: 180 }
      ],
      list: [],
      total: 0,
      tableLoading: false,
      fileUplodVisible: false,
      confirmLoading: false,
      otherFilesData: {
        id: '',
        otherFiles: []
      }
    }
  },
  mounted () {
    this.handleQuery()
  },
  computed: {
    ...mapState('base', ['settleSelectMap']),

    rules () {
      return {
        otherFiles: [
          { required: true, message: this.$t('请上传'), trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    queryTableList () {
      this.tableLoading = true
      const data = {
        cbCstNo: this.$store.state.home.sessionData.session_customerId,
        ...this.formData
      }
      settleApi.orderList(data).then(res => {
        const { list, totalSize } = res
        if (list && list.length) {
          this.list = list.map(item => {
            return {
              ...item,
              materialStatusName: this.fileStatus[item.materialStatus]
            }
          })
        } else {
          this.list = []
        }
        this.total = totalSize
      })
        .catch(console.log)
        .finally(() => {
          this.tableLoading = false
        })
    },
    handleQuery () {
      this.formData.page = '1'
      this.queryTableList()
    },
    handleReset () {
      this.$refs.queryForm.resetFields()
      this.formData.page = '1'
      this.queryTableList()
    },
    handleDetails (row) {
      this.$router.push({
        name: 'SettleRecordsDetails',
        query: {
          id: row.id
        }
      })
    },
    // 重新提交
    handleReSubmit (row) {
      const { withdrawalType: type, id } = row
      // 企业自提 ENTERPRISE_WITHDRAW_DEPOSIT
      // 供应商付款 SUPPLER_PAYMENT
      this.$router.push({
        name: 'SettleStep',
        query: {
          type,
          id
        }
      })
    },
    // 撤销
    handleOrderCancel (row) {
      this.$tconfirm('是否确认撤销该提现申请', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        const { tradeNoJd: tradeNo } = row
        settleApi.orderCancel({
          tradeNo
        }).then(res => {
          this.handleQuery()
        })
      }).catch(_ => {
        console.log(_)
      })
    },
    // 补充文件
    handleAddFile (row) {
      this.otherFilesData.id = row.id
      this.fileUplodVisible = true
    },
    handleConfirm () {
      this.$refs.submitForm.validate((valid) => {
        if (valid) {
          const data = {
            id: this.otherFilesData.id,
            otherFiles: this.otherFilesData.otherFiles.map(item => item.path)
          }
          settleApi.updateSettlementOrderFiles(data).then(res => {
            if (res) {
              this.fileUplodVisible = false
              this.$success('文件补充成功')
              this.$nextTick(() => {
                this.otherFilesData = {
                  id: '',
                  otherFiles: []
                }
              })
            }
          })
        }
      })
    },
    // 申请提现
    handleWithdrow (row) {
      this.$router.push({
        name: 'SettleRecordsApplyWithdrawal',
        query: {
          id: row.id,
          tradeNoJd: row.tradeNoJd
        }
      })
    },
    // 分页
    handleCurrentChange (val) {
      this.formData.page = val
      this.queryTableList()
    }
  }
}
</script>

<style scoped lang="less">
.w100 {
  width: 100%;
}
.row {
  display: flex;
  .el-col + .el-col {
    margin-left: 40px;
  }
}
.split-container {
  display: flex;
  width: 100%;
  .start {
    flex: 1 1 0%;
    flex: 1 1 0%;
  }
  .end {
    flex: 1 1 0%;
    margin-left: 10px;
  }
}
.table {
  margin-top: 40px;
}

.tip-text {
  margin-left: 15px;
  font-size: 12px;
  font-weight: 300;
  color: @blackLess;
  line-height: 18px;
  & > .require {
    position: relative;
    &::before {
      content: '*';
      position: absolute;
      color: @red;
      left: -10px;
      top: 2px;
    }
  }
}
.btns {
  margin-top: 40px;
  display: flex;
  justify-content: flex-end;
}
</style>
